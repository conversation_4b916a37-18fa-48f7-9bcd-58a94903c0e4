export interface LoginData {
    email: string;
    password: string;
    remember_me?: boolean;
}

export interface AuthData {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
}

export interface PasswordConfirmation {
    email: string;
    token: string;
    password: string;
}

export interface PasswordChange {
    old_password: string;
    password_confirmation: string;
    password: string;
}

export interface PasswordChangeForm {
    passwordCurrent: string;
    passwordConfirm: string;
    password: string;
}


export interface User {
    name: string;
    email: string;
}


