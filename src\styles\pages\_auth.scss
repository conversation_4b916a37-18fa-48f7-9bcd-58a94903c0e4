.auth {
    @include flex(flex, row, wrap, flex-start, flex-start);
    height: 100lvh;
    overflow: hidden;

    &.center {
        @include flex(flex, row, wrap, flex-start, flex-start);
        background-color: rgba(0, 0, 0, 1);
        padding: 20px;
        overflow: scroll;
    }

    &-bg {
        @include background-image('login-bg', jpg, cover, center, no-repeat);
    }

    &-content {
        background-color: #fff;
        height: 100lvh;
        overflow: auto;
        padding: 56px 102px;
        width: 50%;

        &.center {
            border-radius: 24px;
            border: 1px solid #B9B9B9;
            height: auto;
            margin: auto;
            padding: 56px;
            width: 600px;
        }
    }

    &-side {
        @include flex(flex, row, wrap, flex-start, flex-start);
        background-color: #000;
        flex: 1;
        height: 100lvh;
        overflow: auto;
        padding: 24px;
        width: 50%;
    }

    &-top {
        margin-bottom: 40px;
        text-align: center;

        h1 {
            margin-bottom: 16px;
        }

        &-text {
            font-size: 18px;
            font-weight: $font-weight-semibold;
        }
    }

    &-button {
        display: block;
        margin-left: auto;
        margin-right: auto;
        margin-top: 16px;
        width: 80%;

        @include breakpoint(sm) {
            width: 100%;
        }

        &-back {
            display: block;
            margin: 18px auto 0;

            span {
                text-decoration: underline;
            }
        }
    }

    &-form {

        &-item {
            padding-bottom: 8px;

            &.big {
                margin-bottom: 40px;
            }

            &-helper {
                @include flex(flex, row, wrap, space-between, center);
                gap: 16px;
                margin-bottom: 8px;

                label {
                    font-size: 16px;
                    font-weight: $font-weight-semibold;
                }

                .button {
                    opacity: .6;
                }
            }
        }
    }

    &-list {
        column-count: 2;
        column-gap: 16px;
        margin: 32px 0;

        &-item {
            @include flex(flex, row, nowrap, flex-start, center);
            color: rgba($color-primary, .3);
            font-size: 14px;
            font-weight: $font-weight-semibold;
            gap: 8px;
            margin-bottom: 8px;
            transition: all .3s ease-in-out;

            svg {
                flex-shrink: 0;
                font-size: 24px;
            }

            &.item-active {
                color: $color-primary;
            }
        }
    }

    // forgot password
    &-fp {

        &-bottom {
            font-size: 18px;
            font-weight: $font-weight-semibold;
            text-align: center;

            .button {
                font-size: 18px;
                font-weight: $font-weight-semibold !important;
            }
        }
    }

    // new password
    &-np {
        .auth-list {
            margin-bottom: 32px;
            margin-top: 32px;
        }
    }

    // register
    &-register {
        @include background-image('login-bg', jpg, cover, center, no-repeat);

        &-top {
            margin-bottom: 40px;

            h1 {
                margin-bottom: 8px;
            }

            &-text {
                font-size: 18px;
                font-weight: $font-weight-semibold;
            }
        }

        &-bottom {
            font-size: 18px;
            margin-top: 18px;
            text-align: center;

            &-label {
                margin-right: 8px;
            }
        }

        &-side {
            color: #fff;
            margin: auto;
            text-align: center;
            position: relative;
            z-index: 1;

            .heading {
                color: #fff;
                margin-bottom: 32px;
            }

            &-text {
                font-size: 20px;
                font-weight: $font-weight-semibold;
            }
        }

        .auth-side {
            @include background-image('login-bg', jpg, cover, center, no-repeat);
            position: relative;

            &:after {
                background-color: rgba(0, 0, 0, .2);
                content: '';
                height: 100%;
                left: 0;
                position: absolute;
                top: 0;
                width: 100%;
                z-index: 0;
            }
        }

        &-group {
            padding: 0 40px;

            &.box {
                border: 1px solid #D8D8D8;
                margin-bottom: 40px;
                padding: 40px;
            }
        }

        &-section {
            @include flex(flex, row, nowrap, flex-start, center);
            gap: 8px;
            margin-bottom: 32px;

            .heading {
                margin-bottom: 0;
            }

            span {
                color: #A6A6A6;
                font-size: 14px;
                font-weight: $font-weight-semibold;
            }
        }

        &-actions {
            @include flex(flex, row, nowrap, space-between, center);
            gap: 16px;
            padding-top: 16px;

            &-link {
                text-decoration: none !important;
            }

            .button {

                &.big {
                    padding-left: 40px;
                    padding-right: 40px;
                }
            }
        }
    }
}
