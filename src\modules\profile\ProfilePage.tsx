import withAuth from '../../hooks/withAuth.tsx';
import Content from '../app/layouts/content/Content.tsx';
import InputField from '../app/shared/form/InputField.tsx';
import { useAuthStore } from '../../store';
import { Col, Form, FormInstance, Row } from 'antd';
import Button from '../app/shared/Button.tsx';
import PasswordField from '../app/shared/form/PasswordField.tsx';
import CustomForm from '../app/shared/form/Form.tsx';
import { RuleObject } from 'antd/es/form';
import {
    hasEightLetterMin,
    hasLowercaseLetter,
    hasNumber,
    hasSymbol,
    hasUppercaseLetter
} from '../../utils/helpers.ts';
import PasswordRequirementsList from '../app/shared/form/PasswordRequirementsList.tsx';
import { useEffect, useState } from 'react';
import { PasswordChangeForm } from '../../store/auth/types/AuthData.ts';
import FlashMessages from '../app/shared/FlashMessages.tsx';

const ProfilePage = () => {
    const [form] = Form.useForm();
    const { user } = useAuthStore();
    const password = Form.useWatch('password', form);
    const passwordConfirm = Form.useWatch('passwordConfirm', form);
    const passwordCurrent = Form.useWatch('passwordCurrent', form);
    const [canSave, setCanSave] = useState<boolean>(false);
    const {changePassword} = useAuthStore();
    const [submitting, setSubmitting] = useState<boolean>(false);
    
    const onSubmit = (fields: PasswordChangeForm) => {
        setSubmitting(true);
        changePassword({
            old_password: fields.passwordCurrent,
            password_confirmation: fields.passwordConfirm,
            password: fields.password,
        }).then(() => {
            form.resetFields();
            FlashMessages.success('Password successfully changed!');
        }).catch((e) => {
            const errors = e.response.data.errors;
            if (errors) {
                const fieldErrors = Object.keys(errors).map((field) => ({
                    name: field,
                    errors: errors[field],
                }));
                form.setFields(fieldErrors);
            }
            const message = e.response?.data?.message ? e.response?.data?.message : e.response?.data
            e.response?.data?.message && FlashMessages.error(message);
        }).finally(() => {
            setSubmitting(false);
        })
    }
    
    useEffect(() => {
        if (!password || !passwordCurrent || !passwordConfirm || passwordConfirm !== password) {
            setCanSave(false);
            return;
        }
        setCanSave(true);
    }, [passwordConfirm, password, passwordCurrent]);
    
    return (
        <Content
            headerTitle="Company Profile"
            className="profile"
        >
            <div className="profile-group">
                <div className="profile-group-head">
                    <h5 className="profile-group-head-title">Account Information</h5>
                </div>
                <div className="profile-group-body form">
                    <Row gutter={[16, 16]} align="middle" className="profile-group-body-row">
                        <Col span={8}>
                            <div className="profile-group-body-row-label">
                                Vendor Name
                            </div>
                        </Col>
                        <Col span={16}>
                            <div className="profile-group-body-row-content">
                                <InputField className="margin-bottom-0" disabled={true} inputProps={{ defaultValue: user?.name }}/>
                            </div>
                        </Col>
                    </Row>
                    <Row gutter={[16, 16]} align="middle" className="profile-group-body-row">
                        <Col span={8}>
                            <div className="profile-group-body-row-label">
                                Email Address
                            </div>
                        </Col>
                        <Col span={16}>
                            <div className="profile-group-body-row-content">
                                <InputField className="margin-bottom-0" disabled={true} inputProps={{ defaultValue: user?.email }}/>
                            </div>
                        </Col>
                    </Row>
                </div>
            </div>
            <div className="profile-group">
                <CustomForm form={form} onSubmit={onSubmit}>
                    <div className="profile-group-head">
                        <h5 className="profile-group-head-title">Change Password</h5>
                        <div className="profile-group-head-actions">
                            <Button
                                color="primary"
                                variant="outlined"
                                htmlType="button"
                                onClick={() => form.resetFields()}
                            >
                                Cancel
                            </Button>
                            <Button
                                color="primary"
                                variant="solid"
                                disabled={!canSave || submitting}
                            >
                                Save Changes
                            </Button>
                        </div>
                    </div>
                    <div className="profile-group-body">
                        <Row gutter={[16, 16]} align="middle" className="profile-group-body-row">
                            <Col span={8}>
                                <div className="profile-group-body-row-label">
                                    Current Password
                                </div>
                            </Col>
                            <Col span={16}>
                                <div className="profile-group-body-row-content">
                                    <PasswordField
                                        name="passwordCurrent"
                                        placeholder="● ● ● ● ● ● ● ● ●"
                                        rules={[{ required: true, message: "Current password is required" }]}
                                    />
                                </div>
                            </Col>
                        </Row>
                        <Row gutter={[16, 16]} align="middle" className="profile-group-body-row">
                            <Col span={8}>
                                <div className="profile-group-body-row-label">
                                    New Password
                                </div>
                            </Col>
                            <Col span={16}>
                                <div className="profile-group-body-row-content">
                                    <PasswordField
                                        name="password"
                                        placeholder="● ● ● ● ● ● ● ● ●"
                                        rules={[
                                            { required: true, message: "Password is required" },
                                            {
                                                validator: (_: RuleObject, value: string) => {
                                                    if (!value) return Promise.resolve();
                                                    const conditions = [
                                                        hasLowercaseLetter(value),
                                                        hasUppercaseLetter(value),
                                                        hasNumber(value),
                                                        hasSymbol(value),
                                                        hasEightLetterMin(value),
                                                    ];
                                                    const allConditionsMet = conditions.every(Boolean);
                                                    
                                                    if (allConditionsMet) {
                                                        return Promise.resolve();
                                                    }
                                                    return Promise.reject(
                                                        new Error("Password does not meet the requirements")
                                                    );
                                                },
                                            },
                                        ]}
                                    />
                                </div>
                            </Col>
                        </Row>
                        <Row gutter={[16, 16]} align="middle" className="profile-group-body-row">
                            <Col span={8}>
                            </Col>
                            <Col span={16}>
                                <div className="profile-group-body-row-content">
                                    <PasswordRequirementsList inputValue={password}/>
                                </div>
                            </Col>
                        </Row>
                        <Row gutter={[16, 16]} align="middle" className="profile-group-body-row">
                                <Col span={8}>
                                    <div className="profile-group-body-row-label">
                                        Confirm Password
                                    </div>
                                </Col>
                                <Col span={16}>
                                    <div className="profile-group-body-row-content">
                                        <PasswordField
                                            className="auth-form-item big"
                                            name="passwordConfirm"
                                            placeholder="● ● ● ● ● ● ● ● ●"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: 'Please confirm your password!',
                                                },
                                                ({ getFieldValue }: { getFieldValue: FormInstance['getFieldValue'] }): RuleObject => ({
                                                    async validator(_: RuleObject, value: string) {
                                                        if (!value || getFieldValue('password') === value) {
                                                            return Promise.resolve();
                                                        }
                                                        return Promise.reject(new Error('The new password that you entered do not match!'));
                                                    },
                                                }),
                                            ]}
                                        />
                                    </div>
                                </Col>
                            </Row>
                    </div>
                </CustomForm>
            </div>
        </Content>
    );
};

export default withAuth(ProfilePage);
