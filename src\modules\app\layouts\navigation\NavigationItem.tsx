import classNames from 'classnames';
import React from 'react';
import { NavLink } from 'react-router-dom';

interface Props {
    className?: string;
    closingDeviceSidebar?: boolean;
    iconBefore?: React.ReactNode;
    itemType?: 'link' | 'static';
    iconBeforeMarked?: boolean;
    end?: boolean;
    label: string;
    onClick?: VoidFunction;
    link?: string;
}

const NavigationItem: React.FC<Props> = ({
    className,
    iconBefore,
    iconBeforeMarked,
    itemType = 'link',
    end = false,
    label,
    onClick,
    link = '/',
}) => {

    return (
        <>
            {itemType == 'link' && (
                <NavLink
                    to={link}
                    className={className}
                    title={label}
                    end={end}
                    onClick={() => {
                        !!onClick && onClick();
                    }}
                >
                    {iconBefore && (
                        <div
                            className={classNames({
                                icon: true,
                                marked: iconBeforeMarked,
                            })}
                        >
                            {iconBefore}
                        </div>
                    )}
                    {label}
                </NavLink>
            )}
            {itemType == 'static' && (
                <div
                    className={classNames(className, 'static')}
                    onClick={() => {
                        !!onClick && onClick();
                    }}
                >
                    {iconBefore && (
                        <div
                            className={classNames({
                                icon: true,
                                marked: iconBeforeMarked,
                            })}
                        >
                            {iconBefore}
                        </div>
                    )}
                    {label}
                </div>
            )}
        </>
    );
};

export default NavigationItem;
