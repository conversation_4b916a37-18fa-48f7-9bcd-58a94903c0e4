.fv {

    &-container {
        min-height: 100px;
        position: relative;
    }

    &-image {
        width: 100%;
    }

    &-bottom {
        @include flex(flex, row, wrap, space-between, center);
        gap: 12px;
        font-size: 12px;
        margin-top: 16px;
    }

    &-content {
        min-height: 100px;

        canvas {
            height: auto !important;
            width: 100% !important;
        }
    }

    &-navigation {

        &-button {
            background-color: #fff;
            border: 1px solid #cacaac;
            font-size: 24px;
            height: 32px;
            padding: 0;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 5;
            width: 32px !important;

            &.next {
                right: 0;
            }

            &.prev {
                left: 0;
            }
        }
    }
}
