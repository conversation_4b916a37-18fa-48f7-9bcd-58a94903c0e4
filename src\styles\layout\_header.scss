.header {
    padding: 48px $content-offset;

    &.secondary {
        @include flex(flex, column, nowrap, flex-start, stretch);
        gap: 24px;
        padding: 32px $content-offset;
    }

    @include breakpoint(md) {
        background-color: $color-background;
        justify-content: flex-start;
    }

    &-content {
        @include flex(flex, row, nowrap, space-between, flex-start);
        gap: 16px;

        &-meta {
            @include flex(flex, column, nowrap, flex-start, stretch);
            gap: 8px;

            .heading {
                margin-bottom: 0;
            }

            &-subtitle {
                margin-bottom: 0;
            }
        }

        &-actions {
            @include flex(flex, row, nowrap, flex-start, center);
            gap: 8px;
        }
    }
}
