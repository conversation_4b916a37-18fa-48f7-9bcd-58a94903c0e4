import React from 'react';
import { Checkbox, Form, FormItemProps } from 'antd';
import classNames from 'classnames';

interface Props extends FormItemProps {
    className?: string;
    fontSize?: 'default' | 'medium';
    label?: React.ReactNode;
    name: string;
    placeholder?: string;
    rules?: any[];
    
}

const CheckboxField: React.FC<Props> = ({ className, fontSize, label, name, rules, ...props }) => {
    return (
        <Form.Item
            className={classNames("form-field", fontSize, className)}
            name={name}
            rules={rules}
            {...props}
        >
            <Checkbox className={classNames("form-field-checkbox", fontSize)}>{label}</Checkbox>
        </Form.Item>
    );
};

export default CheckboxField;
