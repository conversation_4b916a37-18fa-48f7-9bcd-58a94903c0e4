import { toast, ToastOptions, Slide, TypeOptions } from 'react-toastify';
import React, { ReactElement } from 'react';
import { ReactComponent as IconWarning } from '../../../assets/icons/icon-warning.svg';
import { ReactComponent as IconSuccess } from '../../../assets/icons/icon-checkmark-double.svg';
import { ReactComponent as IconInfo } from '../../../assets/icons/icon-info.svg';
import { ReactComponent as IconClose } from '../../../assets/icons/icon-close.svg';

enum ToastTypeOptions {
    INFO = 'info',
    WARN = 'warning',
    ERROR = 'error',
    SUCCESS = 'success',
}

const ToastIcons: Partial<Record<TypeOptions, ReactElement>> = {
    info: <IconInfo />,
    success: <IconSuccess />,
    warning: <IconInfo />,
    error: <IconWarning />,
};

const ToastTitle: Partial<Record<TypeOptions, string>> = {
    info: 'Info!',
    success: 'Success!',
    warning: 'Whoops!',
    error: 'Error!',
};

const message = (content: React.ReactNode, type: TypeOptions = ToastTypeOptions.INFO) => {
    const CloseButton = ({ closeToast }: any) => <IconClose className="icon-regular" onClick={closeToast} />;

    const options: ToastOptions = {
        type,
        draggable: false,
        closeOnClick: false,
        icon: false,
        closeButton: CloseButton,
        transition: Slide,
    };

    toast(
        <>
            <span className="icon">{ToastIcons[type]}</span>
            <div className="Toastify__toast-body-inner">
                <div className="title">{ToastTitle[type]}</div>
                <div className="text">{content}</div>
            </div>
        </>,
        options,
    );
};
const FlashMessages = {
    success: (content: React.ReactNode | undefined) => message(content, ToastTypeOptions.SUCCESS),
    info: (content: React.ReactNode | undefined) => message(content, ToastTypeOptions.INFO),
    warn: (content: React.ReactNode | undefined) => message(content, ToastTypeOptions.WARN),
    error: (content: React.ReactNode | undefined) => message(content, ToastTypeOptions.ERROR),
};

export default FlashMessages;
