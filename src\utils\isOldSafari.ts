
const minVersionSupported = 17;

export const isOldSafari = () => {
    const ua = navigator.userAgent;
    const safariVersionMatch = ua.match(/Version\/(\d+)\.(\d+)/);
    if (safariVersionMatch) {
        const major = parseInt(safariVersionMatch[1], 10);
        // const minor = parseInt(safariVersionMatch[2], 10);
        // return major < minVersionSupported || (major === minVersionSupported && minor < 4);
        return major < minVersionSupported;
    }
    return false;
};
