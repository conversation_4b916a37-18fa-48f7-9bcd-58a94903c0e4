.Toastify {
    &__toast {
        @include flex(flex, row, nowrap, flex-start, center);
        background-color: $color-background-light !important;
        box-shadow: 0 0 20px 10px rgba(0, 0, 0, .1) !important;
        border-radius: 0 !important;
        gap: 16px;
        padding: 20px 24px !important;

        > svg {
            align-self: flex-start;
            color: $color-text !important;
            cursor: pointer;
            flex-shrink: 0;
            font-size: 28px;
        }

        &-body {
            color: $color-text !important;
            font-size: 14px !important;
            margin: 0 !important;
            padding: 0 !important;
            word-break: break-all;

            > div {
                @include flex(flex, row, nowrap, flex-start, flex-start);
            }

            &-inner {

                .title {
                    font-weight: $font-weight-semibold;
                    margin-bottom: 4px;
                }

                .text {
                    font-size: 14px;
                }
            }

            .icon {
                @include flex(flex, row, nowrap, center, center);
                background-color: white;
                border-radius: 50%;
                color: #fff;
                flex-shrink: 0;
                font-size: 20px;
                height: 32px;
                margin-right: 16px;
                width: 32px;

                svg {
                    font-size: 18px;
                }
            }
        }

        &-container {
            background-color: transparent !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .icon-close {
            display: block;
            align-self: flex-start !important;
            height: 16px !important;
            margin: 0;
            width: 16px !important;

            path {
                fill: #fff !important;
            }
        }

        &--info {
            .icon {
                background-color: $color-info !important;
            }
        }

        &--success {
            .icon {
                background-color: $color-success !important;
            }
        }

        &--error {
            .icon {
                background-color: $color-error !important;
            }
        }

        &--warning {
            .icon {
                background-color: $color-warning !important;
            }
        }
    }
}
