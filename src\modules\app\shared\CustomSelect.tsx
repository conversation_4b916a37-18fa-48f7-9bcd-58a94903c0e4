import React from 'react';
import { Select, SelectProps } from 'antd';
import classNames from 'classnames';
import Chevron from './svg/Chevron.tsx';

export interface SelectFormOption {
    label: string;
    value: string;
}

interface Props extends SelectProps {
    className?: string;
    defaultValue?: SelectFormOption;
    disabled?: boolean;
    fontSize?: 'default' | 'medium';
    label?: string;
    onOptionChange?: (option: string) => void;
    options: SelectFormOption[];
    placeholder?: string;
    selectProps?: SelectProps;
    type?: string;
}

const SelectField: React.FC<Props> = ({
    className,
    defaultValue,
    disabled,
    fontSize = 'default',
    label,
    onOptionChange,
    options,
    placeholder,
    selectProps,
}) => {
    return (
        <div
            className={classNames("form-field", className)}
        >
            {label}
            <Select
                className={classNames("form-field-select", fontSize)}
                style={{ width: 120 }}
                disabled={disabled}
                onChange={(value) => !!onOptionChange && onOptionChange(value)}
                placeholder={placeholder}
                defaultValue={defaultValue?.value}
                popupClassName="form-field-select-popup"
                options={options}
                suffixIcon={<Chevron className="icon-regular" direction="down" />}
                {...selectProps}
            />
        </div>
    );
};

export default SelectField;
