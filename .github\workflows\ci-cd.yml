name: React Vite App CI/CD

on:
  push:
    branches: [main, develop]
  workflow_dispatch:

env:
  NODE_VERSION: '18'

jobs:
  build:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}

    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build app
      run: npm run build
      env:
        NODE_ENV: production
        VITE_API_URL: ${{ secrets.VITE_API_URL }}

    - name: Create deployment package
      run: |
        mkdir -p deployment
        # Copy all files and directories except node_modules, .git, and other build artifacts
        rsync -a --exclude='node_modules' --exclude='.git' --exclude='.github' --exclude='deployment' --exclude='.gitignore' . deployment/
        # Ensure dist directory is included with all its contents
        if [ -d "dist" ]; then
          cp -r dist deployment/
        fi
        # Include any environment files if they exist
        if [ -f ".env" ]; then
          cp .env deployment/
        fi

    - name: Deploy to EC2
      run: |
        echo "${{ secrets.EC2_SSH_PRIVATE_KEY }}" > private_key.pem
        chmod 600 private_key.pem

        if [ "$GITHUB_REF" = "refs/heads/main" ]; then
          TARGET_DIR="/home/<USER>/app.safepackandstore.com"
        else
          TARGET_DIR="/home/<USER>/app.spsstaging.com"
        fi

        # Create a tarball of the deployment directory
        tar -czf deployment.tar.gz -C deployment .

        echo "GITHUB_REF: $GITHUB_REF"
        echo "EC2_HOST: ${{ vars.EC2_HOST }}"

        # Copy the tarball to the server
        scp -i private_key.pem -o StrictHostKeyChecking=no deployment.tar.gz ubuntu@${{ vars.EC2_HOST }}:/tmp/

        ssh -i private_key.pem -o StrictHostKeyChecking=no ubuntu@${{ vars.EC2_HOST }} << 'EOL'
          set -e
          
          if [ "$GITHUB_REF" = "refs/heads/main" ]; then
            TARGET_DIR="/home/<USER>/app.safepackandstore.com"
          else
            TARGET_DIR="/home/<USER>/app.spsstaging.com"
          fi

          # Create target directory if it doesn't exist
          sudo mkdir -p "$TARGET_DIR"

          # Create a temporary directory for extraction
          TEMP_DIR="/tmp/deploy-$(date +%s)"
          mkdir -p "$TEMP_DIR"
          
          # Extract files to temporary directory first
          echo "Extracting files to temporary location..."
          tar -xzf /tmp/deployment.tar.gz -C "$TEMP_DIR"
          
          # Ensure target directory is owned by forge
          echo "Setting directory ownership..."
          sudo chown -R forge:www-data "$TARGET_DIR"
          sudo chmod 755 "$TARGET_DIR"
          
          # Copy files as forge user
          echo "Copying files to $TARGET_DIR"
          sudo -u forge rsync -av --delete "$TEMP_DIR/" "$TARGET_DIR/"
          
          # Clean up temporary directory
          rm -rf "$TEMP_DIR"

          # Update dependencies as forge user
          echo "Updating dependencies..."
          cd "$TARGET_DIR"
          
          # Update npm packages without removing node_modules
          echo "Running npm install to update dependencies..."
          sudo -u forge npm install --production

          # Ensure correct permissions
          echo "Setting final permissions..."
          sudo chown -R forge:www-data "$TARGET_DIR"
          sudo find "$TARGET_DIR" -type d -exec chmod 755 {} \;
          sudo find "$TARGET_DIR" -type f -exec chmod 644 {} \;

          # Clean up
          rm -f /tmp/deployment.tar.gz
          echo "Deployment completed successfully!"
          echo "Application is ready to serve"
        EOL