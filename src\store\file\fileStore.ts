import { FileStore } from './types/FileStore.ts';
import { create } from 'zustand/react';
import axios from 'axios';

export const fileStore = create<FileStore> (() => ({
    uploadFileS3: async (file, url) => {
        await axios.request({
            method: 'put',
            url: url,
            data: file,
            headers: {
                'Content-Type': file.type,
            },
        });
        
        return;
    }
}))
