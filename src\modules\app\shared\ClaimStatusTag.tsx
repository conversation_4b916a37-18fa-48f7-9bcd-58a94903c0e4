import React from 'react';
import { ClaimStatus } from '../../../store/claims/types/Claim.ts';
import classNames from 'classnames';
import { getStatusClass } from '../../../utils/getStatusClass.ts';
import StatusDot from './StatusDot.tsx';

interface Props {
    status?: ClaimStatus;
}

const ClaimStatusTag: React.FC<Props> = ({ status }) => {
    if (!status) {
        return;
    }
    return (
        <div className={classNames("cst", getStatusClass(status))}>
            <StatusDot status={status} />
            {status}
        </div>
    );
};

export default ClaimStatusTag;
