import { Button as AntdsgnButton } from "antd";
import React from 'react';
import classNames from 'classnames';
import { ButtonProps } from 'antd/es/button/button';

export enum ButtonVariation {
    PRIMARY = 'button primary',
    SECONDARY = 'button secondary',
    LINK = 'button link',
    LINK_DARK = 'button link dark',
    LINK_SEMIBOLD = 'button link semibold',
    LINK_BOLD = 'button link bold',
}

export enum ButtonSize {
    SMALL = 'small',
    REGULAR = 'regular',
    BIG = 'big',
    LARGE = 'large',
}

interface Props extends ButtonProps {
    children?: React.ReactNode;
    className?: string;
    disabled?: boolean;
    onClick?: VoidFunction;
    buttonSize?: ButtonSize
    variation?: ButtonVariation;
    wide?: boolean;
}

const Button: React.FC<Props> = ({
    children,
    className,
    disabled,
    onClick,
    buttonSize = ButtonSize.REGULAR,
    variation = ButtonVariation.PRIMARY,
    wide,
    ...buttonProps
}) => {
    return (
        <AntdsgnButton
            onClick={onClick}
            disabled={disabled}
            className={classNames(
                'button',
                {
                    disabled,
                    wide
                },
                buttonSize,
                variation,
                className,
                
            )}
            htmlType="submit"
            {...buttonProps}
        >
            {children}
        </AntdsgnButton>
    );
};

export default Button;
