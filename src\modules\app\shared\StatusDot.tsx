import React from 'react';
import { ClaimStatus } from '../../../store/claims/types/Claim.ts';
import classNames from 'classnames';
import { getStatusClass } from '../../../utils/getStatusClass.ts';

interface Props {
    status: ClaimStatus
}

const StatusDot: React.FC<Props> = ({ status }) => {
    
    return (
        <div className={classNames("status-dot", getStatusClass(status))}></div>
    );
}
export default StatusDot;
