.main {
    @include flex(flex, row, nowrap, flex-start, stretch);
    height: 100vh;

    @include breakpoint(md) {
        height: auto;
    }

    &-content {
        @include flex(flex, column, nowrap, flex-start, stretch);
        flex: 1;
        position: relative;
        overflow: auto;
        z-index: 2;

        @include breakpoint(md) {
            overflow: auto;
        }

        &-inner {
            @include flex(flex, column, nowrap, flex-start, initial);
            flex: 1;
            padding-bottom: 30px;
            padding-left: $content-offset;
            padding-right: $content-offset;
        }
    }
}
