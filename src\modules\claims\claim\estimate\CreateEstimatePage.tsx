import React, { useEffect, useState } from 'react';
import Content from '../../../app/layouts/content/Content.tsx';
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Col, Form, Row, Select, Input } from 'antd';
import Button, { ButtonVariation } from '../../../app/shared/Button.tsx';
import ClaimStatusTag from '../../../app/shared/ClaimStatusTag.tsx';
import { Estimate, EstimateItem, PriceBookItem } from '../../../../store/claims/types/Claim.ts';
import { ReactComponent as IconPlus } from '../../../../assets/icons/icon-plus.svg';
import { ReactComponent as IconMinusFilled } from '../../../../assets/icons/icon-minus-rounded-fill.svg';
import { ReactComponent as IconUpload } from '../../../../assets/icons/icon-upload-cloud.svg';
import { useClaimStore, useFileStore } from '../../../../store';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import { formatCurrency, formatNumberOutput } from '../../../../utils/helpers.ts';

import Loader from '../../../app/shared/Loader.tsx';
import classNames from 'classnames';
import { PriceBookSortingGroups } from './utils.ts';
import Dialog from '../../../app/shared/Dialog.tsx';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';
import Modal, { ModalSize } from '../../../app/shared/Modal.tsx';
import FileViewer from '../../../app/shared/FileViewer.tsx';
import FileUpload from '../../../app/shared/form/FileUpload.tsx';

import EstimateConfirmationModal from './EstimateConfirmationModal.tsx';

import { useNavigate, useParams } from 'react-router-dom';

interface Props {
    onChangeFile?: (newFile: File) => void;
    uploadedFile: File | string;
    estimate?: Estimate;
    preservedFormData?: Record<string, any>;
}

const CreateEstimatePage: React.FC<Props> = ({ estimate, uploadedFile, preservedFormData }) => {
    const navigate = useNavigate();
    const params = useParams();
    const [form] = Form.useForm();
    const formValues = Form.useWatch([], form);
    const { getClaimPriceBook, setEstimate, getClaimEstimateDocumentLink } = useClaimStore();
    const { uploadFileS3 } = useFileStore();
    const { currentClaim } = useClaimsContext();
    const [groupedProducts, setGroupedProducts] = useState<Record<string, PriceBookItem[]>>({});
    const [canSubmit, setCanSubmit] = useState<boolean>(true);
    const [infoExpanded, setInfoExpanded] = useState<boolean>(false);
    const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
    const [priceBookLoading, setPriceBookLoading] = useState<boolean>(false);
    const [estimateItems, setEstimateItems] = useState<Record<string, EstimateItem[]>>({});
    const [groupSelectEnabled, setGroupSelectEnabled] = useState<Record<string, boolean>>({});
    const [selectedTab, setSelectedTab] = useState<string>('');
    const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
    const [tempFormValues, setTempFormValues] = useState<Record<string, any>>({});
    const [showPreviewModal, setShowPreviewModal] = useState<boolean>(false);
    const [showFileUploadModal, setShowFileUploadModal] = useState<boolean>(false);
    const [newUploadedFile, setNewUploadedFile] = useState<File | null>(null);
    const [currentFile, setCurrentFile] = useState<File | string>(uploadedFile);

    const groupNames = Object.keys(groupedProducts);

    // Update current file when uploadedFile prop changes
    useEffect(() => {
        setCurrentFile(uploadedFile);
    }, [uploadedFile]);

    const onClose = () => {
        navigate(`/claims/${params.type}/${params.id}`);
    };

    const onSuccess = () => {
        navigate(`/claims/${params.type}/${params.id}`);
    };

    // Load price book data
    useEffect(() => {
        if (!currentClaim?.id) {
            return;
        }

        setPriceBookLoading(true);
        getClaimPriceBook(currentClaim.id)
            .then((data) => {
                console.log('Price book data received:', data);

                // Group products by product_family and sort according to PriceBookSortingGroups
                const grouped = data.reduce<Record<string, PriceBookItem[]>>((acc, product) => {
                    acc[product.product_family] = acc[product.product_family] || [];
                    acc[product.product_family].push(product);
                    return acc;
                }, {});

                // Sort the groups according to PriceBookSortingGroups order
                const sortedGrouped: Record<string, PriceBookItem[]> = {};
                PriceBookSortingGroups.forEach((groupName) => {
                    if (grouped[groupName]) {
                        sortedGrouped[groupName] = grouped[groupName];
                    }
                });

                // Add any remaining groups that weren't in the sorting list
                Object.keys(grouped).forEach((groupName) => {
                    if (!PriceBookSortingGroups.includes(groupName)) {
                        sortedGrouped[groupName] = grouped[groupName];
                    }
                });

                console.log('Grouped and sorted products:', sortedGrouped);
                setGroupedProducts(sortedGrouped);
            })
            .catch((error) => {
                console.error('Error loading price book:', error);
                FlashMessages.error('Failed to load price book data');
            })
            .finally(() => {
                setPriceBookLoading(false);
            });
    }, [currentClaim?.id, getClaimPriceBook]);

    // Load existing estimate data if editing
    useEffect(() => {
        if (estimate?.items && estimate.items.length > 0) {
            console.log('Loading existing estimate data for editing:', estimate.items);

            // Group estimate items by product family and set form values
            const groupedEstimateData: Record<string, any[]> = {};

            estimate.items.forEach((item) => {
                if (!groupedEstimateData[item.product_family]) {
                    groupedEstimateData[item.product_family] = [];
                }

                groupedEstimateData[item.product_family].push({
                    name: item.product_name,
                    product_id: item.product_id,
                    quantity: item.quantity,
                    unit_price: item.unit_price,
                    // Add length for Storage items if it exists
                    ...(item.product_family === 'Storage' &&
                        (item as any).length && {
                            length: (item as any).length,
                        }),
                });
            });

            console.log('Grouped estimate data for form:', groupedEstimateData);

            // Set form values
            Object.keys(groupedEstimateData).forEach((groupName) => {
                form.setFieldValue(groupName, groupedEstimateData[groupName]);
            });

            console.log('Form values after loading estimate:', form.getFieldsValue());
        }
    }, [estimate?.items, form]);

    // Set initial selected tab
    useEffect(() => {
        if (!selectedTab && Object.keys(groupedProducts).length > 0) {
            const firstTab = Object.keys(groupedProducts)[0];
            console.log('Setting initial selectedTab to:', firstTab);
            setSelectedTab(firstTab);
        }
    }, [groupedProducts]);

    // Debug form values
    useEffect(() => {
        console.log('Form values changed:', formValues);
        console.log('Selected tab:', selectedTab);
    }, [formValues, selectedTab]);

    useEffect(() => {
        if (!estimate?.items) {
            return;
        }
        setEstimateItems(
            estimate.items.reduce<Record<string, EstimateItem[]>>((acc, product) => {
                acc[product.product_family] = acc[product.product_family] || [];
                acc[product.product_family].push(product);
                return acc;
            }, {}),
        );
    }, [estimate?.items]);

    // Restore form values when preserved form data is available
    useEffect(() => {
        if (preservedFormData && Object.keys(preservedFormData).length > 0) {
            console.log('Restoring form values from preserved data:', preservedFormData);

            // Set form values for all groups
            Object.keys(preservedFormData).forEach((groupName) => {
                const groupData = preservedFormData[groupName];
                if (Array.isArray(groupData) && groupData.length > 0) {
                    console.log(
                        `Restoring ${groupName} with ${groupData.length} items:`,
                        groupData,
                    );
                    form.setFieldValue(groupName, groupData);
                }
            });

            // Set the form values all at once as well (backup method)
            form.setFieldsValue(preservedFormData);

            console.log('Form values after restoration:', form.getFieldsValue());
        }
    }, [preservedFormData, form]);

    const handleGroupSelectEnabled = (groupSelect: string, value: boolean) => {
        setGroupSelectEnabled((prevState) => ({
            ...prevState,
            [groupSelect]: value,
        }));
    };

    const onSubmit = async (values: any) => {
        console.log('Form submission values:', values);

        if (!currentClaim?.id) {
            FlashMessages.error('No claim selected');
            return;
        }

        setCanSubmit(false);

        try {
            // Handle file upload if needed
            let documentLink = estimate?.original_estimate || '';
            if (typeof currentFile === 'object') {
                const name = currentFile.name;
                const documentLinkObject = await getClaimEstimateDocumentLink(
                    currentClaim?.id,
                    name,
                );
                await uploadFileS3(currentFile, documentLinkObject.presigned_url);
                documentLink = documentLinkObject.url;
            } else if (typeof currentFile === 'string') {
                documentLink = currentFile;
            }

            console.log('=== SUBMIT DEBUG ===');
            console.log('Raw fields data:', JSON.stringify(values, null, 2));
            console.log('Object.keys(values):', Object.keys(values));
            console.log('Object.values(values):', Object.values(values));

            const products: any[] = Object.values(values).flat() as any[];

            console.log('Flattened products for API:', JSON.stringify(products, null, 2));
            console.log('Total products count:', products.length);
            console.log('=== END SUBMIT DEBUG ===');

            const createdEstimate = await setEstimate(currentClaim?.id, {
                original_estimate: documentLink,
                line_item_description: currentClaim?.project_description || undefined,
                products: products,
            });

            FlashMessages.success('Estimate created successfully!');
            onSuccess();
        } catch (error) {
            console.error('Error submitting form:', error);
            FlashMessages.error('Something went wrong, please try again!');
        } finally {
            setCanSubmit(true);
        }
    };

    const processFormDataToItems = (formData: Record<string, any>): EstimateItem[] => {
        console.log('Processing form data to EstimateItems:', JSON.stringify(formData, null, 2));

        const items: EstimateItem[] = [];

        Object.keys(formData).forEach((category) => {
            console.log(`Processing category: ${category}`);
            const categoryData = formData[category];

            if (Array.isArray(categoryData) && categoryData.length > 0) {
                console.log(`  ✅ Category ${category} has ${categoryData.length} items`);

                categoryData.forEach((item: any, index: number) => {
                    console.log(`    Processing item ${index}:`, item);

                    if (item && (item.product_id || item.name)) {
                        // Include items that have been properly added
                        const estimateItem: EstimateItem & { length?: number } = {
                            product_id: item.product_id || '',
                            quantity: Number(item.quantity) || 0,
                            unit_price: Number(item.unit_price) || 0,
                            product_name: item.name || '',
                            product_description: '',
                            product_family: category,
                        };

                        // Add length field for Storage items
                        if (category === 'Storage' && item.length) {
                            estimateItem.length = Number(item.length);
                        }

                        console.log(
                            `    ✅ ADDING estimate item:`,
                            JSON.stringify(estimateItem, null, 4),
                        );
                        items.push(estimateItem);
                    } else {
                        console.log(`    ❌ SKIPPING item (incomplete):`, item);
                    }
                });
            } else {
                console.log(`  ❌ Category ${category} is not an array or is empty`);
            }
        });

        console.log(`Final processed items (${items.length} total):`, items);
        return items;
    };

    const handleEstimateSummary = () => {
        console.log('=== ESTIMATE SUMMARY DEBUG ===');
        console.log('Getting estimate summary for all tabs');
        console.log('Available group names:', groupNames);
        console.log('Current selectedTab:', selectedTab);

        // Get ALL form data from ALL tabs
        const allFormValues = form.getFieldsValue();
        console.log('All form values:', JSON.stringify(allFormValues, null, 2));

        // Double-check by getting each group individually
        const completeFormData: Record<string, any> = {};
        groupNames.forEach((groupName) => {
            const groupValue = form.getFieldValue(groupName);
            if (groupValue && Array.isArray(groupValue) && groupValue.length > 0) {
                completeFormData[groupName] = groupValue;
            }
        });

        console.log('Complete form data:', JSON.stringify(completeFormData, null, 2));

        // Use the more complete dataset
        const dataToProcess =
            Object.keys(completeFormData).length > 0 ? completeFormData : allFormValues;
        console.log(
            'Using data source:',
            Object.keys(completeFormData).length > 0 ? 'completeFormData' : 'allFormValues',
        );

        // Process form data into EstimateItem format
        const processedItems = processFormDataToItems(dataToProcess);

        if (processedItems.length === 0) {
            FlashMessages.error('Please add at least one service before proceeding to summary.');
            return;
        }

        // Navigate to summary page with the processed items
        navigate(`/claims/${params.type}/${params.id}/estimate/summary`, {
            state: {
                items: processedItems,
                formData: dataToProcess,
                newUploadedFile,
                currentFile,
                estimate,
            },
        });
    };

    const handleConfirmationYes = () => {
        setShowConfirmationModal(false);
        handleEstimateSummary();
    };

    const handleConfirmationNo = () => {
        setShowConfirmationModal(false);
    };

    const handlePreviewFile = () => {
        setShowPreviewModal(true);
    };

    const handleChangeFile = () => {
        // Open the internal file upload modal for file replacement
        setShowFileUploadModal(true);
    };

    const handleFileUploadSubmit = () => {
        if (!newUploadedFile) {
            FlashMessages.error('Please select a file to upload.');
            return;
        }
        // The actual file replacement will be handled in handleUploadFinish
        // when the FileUpload component completes the upload process
        console.log('Form submitted, upload process will handle file replacement');
    };

    const handleFileSelected = (file: File) => {
        setNewUploadedFile(file);
    };

    const handleUploadFinish = async (uploadSuccess: boolean) => {
        if (uploadSuccess && newUploadedFile && currentClaim?.id) {
            try {
                console.log('Upload completed for file:', newUploadedFile.name);

                // Upload the file to S3 via API
                const documentLinkObject = await getClaimEstimateDocumentLink(
                    currentClaim.id,
                    newUploadedFile.name,
                );
                await uploadFileS3(newUploadedFile, documentLinkObject.presigned_url);

                // Update the local current file state for immediate preview
                setCurrentFile(newUploadedFile);

                // Show success message
                FlashMessages.success(
                    `File "${newUploadedFile.name}" has been successfully uploaded and replaced!`,
                );

                // Close the modal and reset state
                setShowFileUploadModal(false);
                setNewUploadedFile(null);
            } catch (error) {
                console.error('Error replacing file:', error);
                FlashMessages.error('Failed to replace the file. Please try again.');
            }
        } else {
            console.log('Upload failed or no file selected');
        }
    };

    return (
        <Content
            headerTitle="Add Services"
            breadcrumbItems={[
                { title: 'Projects', href: `/claims/${params.type}` },
                {
                    title: currentClaim?.claim_name || 'Claim',
                    href: `/claims/${params.type}/${params.id}`,
                },
                { title: 'Add Services' },
            ]}
        >
            <div className="claim-ce" style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                {/* Top content - moved from sidebar */}
                <div
                    className="claim-ce-top-section"
                    style={{
                        width: '100%',
                        background: '#f8f9fa',
                        padding: '20px',
                        borderRadius: '8px',
                        display: 'flex',
                        gap: '24px',
                        alignItems: 'flex-start',
                    }}
                >
                    {/* Left side - Claim header and info */}
                    <div style={{ flex: '1', minWidth: '0' }}>
                        <div className="claim-header" style={{ marginBottom: '24px' }}>
                            <h1 className="heading h3">{currentClaim?.claim_name}</h1>
                            <ClaimStatusTag status={currentClaim?.status} />
                        </div>

                        <div className="claim-ce-content">
                            <div className="claim-ce-info" style={{ marginBottom: '0px' }}>
                                <div
                                    className={classNames('claim-ce-info-inner', {
                                        expanded: infoExpanded,
                                    })}
                                    style={{ background: 'none', paddingTop: '0px' }}
                                >
                                    <h5 className="claim-ce-info-title">Create your estimate</h5>
                                    <div className="claim-ce-info-group">
                                        <p className="claim-ce-info-group-description">
                                            Now it's time to input your line items. Using our service
                                            page, select a service and enter the corresponding rate and
                                            quantity.
                                        </p>
                                        <div
                                            className="claim-ce-info-action"
                                            onClick={() => setInfoExpanded(!infoExpanded)}
                                            style={{
                                                marginTop: '8px',
                                                cursor: 'pointer',
                                                display: 'inline-block',
                                            }}
                                        >
                                            {infoExpanded ? 'Less' : 'Learn More'}
                                        </div>
                                    </div>

                                    {infoExpanded && (
                                        <>
                                            <div className="claim-ce-info-group">
                                                <h6 className="claim-ce-info-group-title">
                                                    Helpful Tips!
                                                </h6>
                                                <p className="claim-ce-info-group-description">
                                                    For materials, group them under one service and
                                                    input the total cost for all items from your
                                                    original estimate. This will streamline the process
                                                    and ensure accuracy. Pack Out = Move Out. Pack Back
                                                    = Move Back.
                                                </p>
                                            </div>
                                            <div className="claim-ce-info-group">
                                                <p className="claim-ce-info-group-description">
                                                    <b>Storage:</b> Select the type of storage required
                                                    (e.g., vault or lb) and enter the estimated storage
                                                    needed in Qty. Storage is estimated for one month.
                                                </p>
                                            </div>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right side - Original estimate section */}
                    <div style={{ flex: '0 0 300px' }}>
                        <div
                            className="claim-ce-viewer-head"
                            style={{
                                padding: '0',
                                margin: '0',
                                borderTop: 'none',
                            }}
                        >
                            <h6 className="claim-ce-viewer-head-title">Original estimate</h6>

                            <div className="claim-ce-viewer-head-actions">
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handlePreviewFile}
                                    className="claim-ce-viewer-head-actions-preview"
                                >
                                    Preview
                                </Button>
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handleChangeFile}
                                    className="claim-ce-viewer-head-actions-change"
                                >
                                    Change
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main content - now full width */}
                <div className="claim-ce-main" style={{ width: '100%' }}>
                    {!canSubmit && <Loader className="claim-ce-loader" />}

                    <CustomForm form={form} onSubmit={onSubmit} className="claim-ce-form">
                        {priceBookLoading && <Loader />}

                        {!priceBookLoading && groupNames.length > 0 && (
                            <div className="claim-ce-tab-layout">
                                <div className="claim-ce-tabs">
                                    {groupNames.map((group: string) => (
                                        <div
                                            key={group}
                                            className={`claim-ce-tab${selectedTab === group ? ' selected' : ''}`}
                                            onClick={() => {
                                                console.log('Switching to tab:', group);
                                                setSelectedTab(group);
                                            }}
                                        >
                                            {group}
                                        </div>
                                    ))}
                                </div>
                                <div className="claim-ce-tab-content">
                                    {/* Render the table and form for the selected tab only */}
                                    {(() => {
                                        const groupName = selectedTab;
                                        const selectOptions =
                                            groupedProducts[groupName]?.map((product) => {
                                                const estimateProductItem = estimateItems[
                                                    groupName
                                                ]?.find(
                                                    (item) =>
                                                        item.product_id === product.product_id,
                                                );
                                                return {
                                                    pricebook_entry_id: product.id,
                                                    label: product.product_name,
                                                    name: product.product_name,
                                                    value: product.id,
                                                    unit_price: estimateProductItem?.unit_price
                                                        ? formatNumberOutput(
                                                              estimateProductItem.unit_price,
                                                          )
                                                        : undefined,
                                                    product_id: product.product_id,
                                                    quantity: estimateProductItem?.quantity
                                                        ? estimateProductItem.quantity
                                                        : 1,
                                                };
                                            }) || [];
                                        const initialValue =
                                            (!!Object.keys(estimateItems).length &&
                                                estimateItems[groupName] &&
                                                selectOptions.filter((item) =>
                                                    estimateItems[groupName].find(
                                                        (estimateItem) =>
                                                            estimateItem.product_id ===
                                                            item.product_id,
                                                    ),
                                                )) ||
                                            [];
                                        return (
                                            <Form.List name={groupName} initialValue={initialValue}>
                                                {(fields, { add, remove }) => (
                                                    <>
                                                        <div className="claim-ce-form-boxes-item-head">
                                                            <Row
                                                                gutter={16}
                                                                align="bottom"
                                                                style={{ width: '100%' }}
                                                            >
                                                                <Col
                                                                    span={
                                                                        groupName === 'Storage'
                                                                            ? 6
                                                                            : 8
                                                                    }
                                                                >
                                                                    <Select
                                                                        className="select"
                                                                        placeholder="Select a Service"
                                                                        options={selectOptions}
                                                                        value={
                                                                            tempFormValues[
                                                                                `${groupName}_select`
                                                                            ]
                                                                        }
                                                                        onChange={(value) => {
                                                                            console.log(
                                                                                'Select option changed:',
                                                                                value,
                                                                                'for group:',
                                                                                groupName,
                                                                            );
                                                                            setTempFormValues(
                                                                                (prev) => {
                                                                                    const newValues =
                                                                                        {
                                                                                            ...prev,
                                                                                            [`${groupName}_select`]:
                                                                                                value,
                                                                                        };
                                                                                    console.log(
                                                                                        'Updated tempFormValues:',
                                                                                        newValues,
                                                                                    );
                                                                                    return newValues;
                                                                                },
                                                                            );
                                                                            handleGroupSelectEnabled(
                                                                                `${groupName}-select`,
                                                                                true,
                                                                            );
                                                                        }}
                                                                        style={{ width: '100%' }}
                                                                    />
                                                                </Col>
                                                                {groupName === 'Storage' && (
                                                                    <Col span={4}>
                                                                        <Input
                                                                            type="number"
                                                                            placeholder="Length (Months)"
                                                                            className="input-wrapper"
                                                                            value={
                                                                                tempFormValues[
                                                                                    `${groupName}_length`
                                                                                ] || ''
                                                                            }
                                                                            onChange={(e) => {
                                                                                setTempFormValues(
                                                                                    (prev) => ({
                                                                                        ...prev,
                                                                                        [`${groupName}_length`]:
                                                                                            e.target
                                                                                                .value,
                                                                                    }),
                                                                                );
                                                                            }}
                                                                            style={{
                                                                                width: '100%',
                                                                            }}
                                                                        />
                                                                    </Col>
                                                                )}
                                                                <Col span={4}>
                                                                    <Input
                                                                        type="number"
                                                                        placeholder="#"
                                                                        className="input-wrapper"
                                                                        value={
                                                                            tempFormValues[
                                                                                `${groupName}_quantity`
                                                                            ] || ''
                                                                        }
                                                                        onChange={(e) => {
                                                                            console.log(
                                                                                'Quantity changed:',
                                                                                e.target.value,
                                                                                'for group:',
                                                                                groupName,
                                                                            );
                                                                            setTempFormValues(
                                                                                (prev) => {
                                                                                    const newValues =
                                                                                        {
                                                                                            ...prev,
                                                                                            [`${groupName}_quantity`]:
                                                                                                e
                                                                                                    .target
                                                                                                    .value,
                                                                                        };
                                                                                    console.log(
                                                                                        'Updated tempFormValues:',
                                                                                        newValues,
                                                                                    );
                                                                                    return newValues;
                                                                                },
                                                                            );
                                                                        }}
                                                                        style={{ width: '100%' }}
                                                                    />
                                                                </Col>
                                                                <Col span={4}>
                                                                    <Input
                                                                        type="number"
                                                                        placeholder="Rate"
                                                                        className="input-wrapper"
                                                                        value={
                                                                            tempFormValues[
                                                                                `${groupName}_rate`
                                                                            ] || ''
                                                                        }
                                                                        onChange={(e) => {
                                                                            setTempFormValues(
                                                                                (prev) => ({
                                                                                    ...prev,
                                                                                    [`${groupName}_rate`]:
                                                                                        e.target
                                                                                            .value,
                                                                                }),
                                                                            );
                                                                        }}
                                                                        style={{ width: '100%' }}
                                                                    />
                                                                </Col>
                                                                <Col
                                                                    span={
                                                                        groupName === 'Storage'
                                                                            ? 6
                                                                            : 4
                                                                    }
                                                                >
                                                                    <Form.Item
                                                                        style={{ margin: 0 }}
                                                                    >
                                                                        <Button
                                                                            color="primary"
                                                                            variant="solid"
                                                                            htmlType="button"
                                                                            onClick={() => {
                                                                                console.log(
                                                                                    'ADD button clicked for group:',
                                                                                    groupName,
                                                                                );
                                                                                console.log(
                                                                                    'Current selectedTab:',
                                                                                    selectedTab,
                                                                                );
                                                                                console.log(
                                                                                    'All tempFormValues:',
                                                                                    tempFormValues,
                                                                                );

                                                                                const selectValue =
                                                                                    tempFormValues[
                                                                                        `${groupName}_select`
                                                                                    ];
                                                                                const lengthValue =
                                                                                    groupName ===
                                                                                    'Storage'
                                                                                        ? tempFormValues[
                                                                                              `${groupName}_length`
                                                                                          ]
                                                                                        : undefined;
                                                                                const quantityValue =
                                                                                    tempFormValues[
                                                                                        `${groupName}_quantity`
                                                                                    ];
                                                                                const rateValue =
                                                                                    tempFormValues[
                                                                                        `${groupName}_rate`
                                                                                    ];

                                                                                console.log(
                                                                                    'Form values for current group:',
                                                                                    {
                                                                                        selectValue,
                                                                                        lengthValue,
                                                                                        quantityValue,
                                                                                        rateValue,
                                                                                        groupName,
                                                                                    },
                                                                                );

                                                                                const item =
                                                                                    selectOptions.find(
                                                                                        (item) =>
                                                                                            item.value ===
                                                                                            selectValue,
                                                                                    );

                                                                                console.log(
                                                                                    'Found item:',
                                                                                    item,
                                                                                );

                                                                                if (
                                                                                    item &&
                                                                                    quantityValue &&
                                                                                    rateValue
                                                                                ) {
                                                                                    const newItem =
                                                                                        {
                                                                                            name: item.label,
                                                                                            pricebook_entry_id:
                                                                                                item.pricebook_entry_id,
                                                                                            product_id:
                                                                                                item.product_id,
                                                                                            quantity:
                                                                                                Number(
                                                                                                    quantityValue,
                                                                                                ),
                                                                                            unit_price:
                                                                                                Number(
                                                                                                    rateValue,
                                                                                                ),
                                                                                            length: lengthValue
                                                                                                ? Number(
                                                                                                      lengthValue,
                                                                                                  )
                                                                                                : undefined,
                                                                                        };

                                                                                    console.log(
                                                                                        'Adding item to group:',
                                                                                        groupName,
                                                                                        'Item:',
                                                                                        newItem,
                                                                                    );
                                                                                    console.log(
                                                                                        'Current form values before add:',
                                                                                        form.getFieldsValue(),
                                                                                    );
                                                                                    add(newItem);
                                                                                    console.log(
                                                                                        'Current form values after add:',
                                                                                        form.getFieldsValue(),
                                                                                    );
                                                                                    handleGroupSelectEnabled(
                                                                                        `${groupName}-select`,
                                                                                        false,
                                                                                    );
                                                                                    // Clear temp form values
                                                                                    console.log(
                                                                                        'Clearing form values for group:',
                                                                                        groupName,
                                                                                    );
                                                                                    setTempFormValues(
                                                                                        (prev) => {
                                                                                            const newValues =
                                                                                                {
                                                                                                    ...prev,
                                                                                                    [`${groupName}_select`]:
                                                                                                        undefined,
                                                                                                    [`${groupName}_length`]:
                                                                                                        undefined,
                                                                                                    [`${groupName}_quantity`]:
                                                                                                        undefined,
                                                                                                    [`${groupName}_rate`]:
                                                                                                        undefined,
                                                                                                };
                                                                                            console.log(
                                                                                                'Cleared tempFormValues:',
                                                                                                newValues,
                                                                                            );
                                                                                            return newValues;
                                                                                        },
                                                                                    );
                                                                                } else {
                                                                                    console.log(
                                                                                        'Missing required fields:',
                                                                                        {
                                                                                            hasItem:
                                                                                                !!item,
                                                                                            hasQuantity:
                                                                                                !!quantityValue,
                                                                                            hasRate:
                                                                                                !!rateValue,
                                                                                        },
                                                                                    );
                                                                                }
                                                                            }}
                                                                            size="large"
                                                                            className="margin-right-16"
                                                                            wide={true}
                                                                            disabled={(() => {
                                                                                const selectValue =
                                                                                    tempFormValues[
                                                                                        `${groupName}_select`
                                                                                    ];
                                                                                const quantityValue =
                                                                                    tempFormValues[
                                                                                        `${groupName}_quantity`
                                                                                    ];
                                                                                const rateValue =
                                                                                    tempFormValues[
                                                                                        `${groupName}_rate`
                                                                                    ];

                                                                                const isDisabled =
                                                                                    !selectValue ||
                                                                                    !quantityValue ||
                                                                                    !rateValue;

                                                                                console.log(
                                                                                    'Button disabled check:',
                                                                                    {
                                                                                        groupName,
                                                                                        selectValue,
                                                                                        quantityValue,
                                                                                        rateValue,
                                                                                        isDisabled,
                                                                                        tempFormValues,
                                                                                    },
                                                                                );

                                                                                return isDisabled;
                                                                            })()}
                                                                            icon={
                                                                                <IconPlus className="icon-regular" />
                                                                            }
                                                                            style={{
                                                                                width: '100%',
                                                                            }}
                                                                        >
                                                                            ADD
                                                                        </Button>
                                                                    </Form.Item>
                                                                </Col>
                                                            </Row>
                                                        </div>
                                                        <div className="claim-ce-form-boxes-item-list">
                                                            <Row className="claim-ce-form-boxes-item-list-head">
                                                                <Col
                                                                    span={
                                                                        groupName === 'Storage'
                                                                            ? 6
                                                                            : 8
                                                                    }
                                                                >
                                                                    Service Title
                                                                </Col>
                                                                {groupName === 'Storage' && (
                                                                    <Col span={4}>
                                                                        Length (Months)
                                                                    </Col>
                                                                )}
                                                                <Col span={4}>#</Col>
                                                                <Col span={4}>Rate</Col>
                                                                <Col span={4} className="end">
                                                                    Total
                                                                </Col>
                                                                <Col span={2}></Col>
                                                            </Row>
                                                            <div className="claim-ce-form-boxes-item-list-body">
                                                                {(() => {
                                                                    console.log(
                                                                        'Fields array:',
                                                                        fields,
                                                                    );
                                                                    return null;
                                                                })()}
                                                                {fields &&
                                                                    fields.map((field, index) => {
                                                                        console.log(
                                                                            'Processing field:',
                                                                            field,
                                                                            'index:',
                                                                            index,
                                                                        );
                                                                        const selectedServiceName =
                                                                            form.getFieldValue([
                                                                                groupName,
                                                                                field.name,
                                                                                'name',
                                                                            ]);
                                                                        const selectedServicePrice =
                                                                            form.getFieldValue([
                                                                                groupName,
                                                                                field.name,
                                                                                'unit_price',
                                                                            ]);
                                                                        const selectedServiceQuantity =
                                                                            form.getFieldValue([
                                                                                groupName,
                                                                                field.name,
                                                                                'quantity',
                                                                            ]);
                                                                        const selectedServiceLength =
                                                                            form.getFieldValue([
                                                                                groupName,
                                                                                field.name,
                                                                                'length',
                                                                            ]);
                                                                        return (
                                                                            <Row key={index}>
                                                                                <Col
                                                                                    span={
                                                                                        groupName ===
                                                                                        'Storage'
                                                                                            ? 6
                                                                                            : 8
                                                                                    }
                                                                                >
                                                                                    <Form.Item
                                                                                        name={[
                                                                                            field.name,
                                                                                            'name',
                                                                                        ]}
                                                                                        noStyle
                                                                                    >
                                                                                        <span>
                                                                                            {selectedServiceName ||
                                                                                                'No name selected'}
                                                                                        </span>
                                                                                    </Form.Item>
                                                                                </Col>
                                                                                {groupName ===
                                                                                    'Storage' && (
                                                                                    <Col span={4}>
                                                                                        <span>
                                                                                            {selectedServiceLength ||
                                                                                                selectedServiceQuantity}
                                                                                        </span>
                                                                                    </Col>
                                                                                )}
                                                                                <Col span={4}>
                                                                                    <span>
                                                                                        {groupName ===
                                                                                        'Storage'
                                                                                            ? selectedServiceQuantity
                                                                                            : `${selectedServiceQuantity} hours`}
                                                                                    </span>
                                                                                </Col>
                                                                                <Col span={4}>
                                                                                    <span>
                                                                                        {formatCurrency(
                                                                                            selectedServicePrice ||
                                                                                                0,
                                                                                        )}
                                                                                    </span>
                                                                                </Col>
                                                                                <Col
                                                                                    span={4}
                                                                                    className="end"
                                                                                >
                                                                                    <span className="claim-ce-form-boxes-item-list-body-total">
                                                                                        {formatCurrency(
                                                                                            (() => {
                                                                                                const baseTotal =
                                                                                                    selectedServiceQuantity *
                                                                                                        selectedServicePrice ||
                                                                                                    0;
                                                                                                // For Storage items, multiply by length
                                                                                                if (
                                                                                                    groupName ===
                                                                                                        'Storage' &&
                                                                                                    selectedServiceLength
                                                                                                ) {
                                                                                                    return (
                                                                                                        baseTotal *
                                                                                                        selectedServiceLength
                                                                                                    );
                                                                                                }
                                                                                                return baseTotal;
                                                                                            })(),
                                                                                        )}
                                                                                    </span>
                                                                                </Col>
                                                                                <Col
                                                                                    span={2}
                                                                                    className="end"
                                                                                >
                                                                                    <IconMinusFilled
                                                                                        className="icon-regular cursor"
                                                                                        onClick={() =>
                                                                                            remove(
                                                                                                field.name,
                                                                                            )
                                                                                        }
                                                                                    />
                                                                                </Col>
                                                                            </Row>
                                                                        );
                                                                    })}
                                                            </div>
                                                        </div>
                                                        {/* Subtotal row */}
                                                        <div className="claim-ce-form-boxes-item-subtotal-row">
                                                            <span className="claim-ce-form-boxes-item-subtotal-label">
                                                                {groupName} Subtotal
                                                            </span>
                                                            <span className="claim-ce-form-boxes-item-subtotal-value">
                                                                {formatCurrency(
                                                                    (() => {
                                                                        // Get all form values to ensure we have data from all tabs
                                                                        const allFormValues =
                                                                            form.getFieldsValue();
                                                                        const groupData =
                                                                            allFormValues[
                                                                                groupName
                                                                            ];

                                                                        return (
                                                                            groupData?.reduce?.(
                                                                                (
                                                                                    sum: number,
                                                                                    item: any,
                                                                                ) => {
                                                                                    const baseTotal =
                                                                                        Number(
                                                                                            item.unit_price,
                                                                                        ) *
                                                                                        Number(
                                                                                            item.quantity,
                                                                                        );
                                                                                    // For Storage items, multiply by length if it exists
                                                                                    if (
                                                                                        groupName ===
                                                                                            'Storage' &&
                                                                                        item.length
                                                                                    ) {
                                                                                        return (
                                                                                            sum +
                                                                                            baseTotal *
                                                                                                Number(
                                                                                                    item.length,
                                                                                                )
                                                                                        );
                                                                                    }
                                                                                    return (
                                                                                        sum +
                                                                                        baseTotal
                                                                                    );
                                                                                },
                                                                                0,
                                                                            ) || 0
                                                                        );
                                                                    })(),
                                                                )}
                                                            </span>
                                                        </div>
                                                    </>
                                                )}
                                            </Form.List>
                                        );
                                    })()}
                                </div>
                            </div>
                        )}

                        {!priceBookLoading && !groupNames.length && (
                            <p>
                                We're updating the list of available services and it'll be ready
                                soon! If you need any help in the meantime, don't hesitate to reach
                                out to us.
                            </p>
                        )}
                    </CustomForm>

                    {/* ESTIMATE SUMMARY Button */}
                    <div className="estimate-summary-button-container">
                        <Button
                            color="primary"
                            variant="solid"
                            onClick={() => setShowConfirmationModal(true)}
                            className="estimate-summary-button"
                            size="large"
                        >
                            ESTIMATE SUMMARY
                        </Button>
                    </div>
                </div>
            </div>

            {showConfirmModal && (
                <Dialog
                    show={showConfirmModal}
                    onClose={() => setShowConfirmModal(false)}
                    onSuccess={() => onClose()}
                    description="If you leave this page, all the information you've entered will be lost. Are you sure you want to continue?"
                />
            )}

            {showConfirmationModal && (
                <EstimateConfirmationModal
                    show={showConfirmationModal}
                    onClose={() => setShowConfirmationModal(false)}
                    onYes={handleConfirmationYes}
                    onNo={handleConfirmationNo}
                />
            )}

            {/* Preview Modal */}
            {showPreviewModal && (
                <Modal
                    show={showPreviewModal}
                    onClose={() => setShowPreviewModal(false)}
                    size={ModalSize.BIG}
                    title="Original Estimate Preview"
                    className="claim-ce-preview-modal"
                >
                    <div className="claim-ce-preview-content">
                        <FileViewer file={currentFile} />
                    </div>
                </Modal>
            )}

            {/* File Upload Modal */}
            {showFileUploadModal && (
                <Modal
                    show={showFileUploadModal}
                    onClose={() => setShowFileUploadModal(false)}
                    size={ModalSize.MEDIUM}
                    title="Change Original Estimate"
                    className="claim-ce-file-upload-modal"
                >
                    <div className="claim-ce-file-upload-content">
                        <CustomForm id="file-upload-form" onSubmit={handleFileUploadSubmit}>
                            <FileUpload
                                name="file"
                                label="Upload new estimate file"
                                rules={[{ required: true, message: 'Please upload a file!' }]}
                                selectedFile={handleFileSelected}
                                onFinish={handleUploadFinish}
                                maxFileSize={10}
                                showProgressBar={true}
                            >
                                <div className="claim-ce-upload" aria-label="File upload area">
                                    <IconUpload
                                        className="claim-ce-upload-icon"
                                        aria-label="Upload icon"
                                    />
                                    <div className="claim-ce-upload-description">
                                        <div className="claim-ce-upload-description-title">
                                            Select a file or drag and drop here
                                        </div>
                                        <div className="claim-ce-upload-description-label">
                                            (JPG, PNG or PDF, file size no more than 10MB)
                                        </div>
                                    </div>
                                    <Button size="large" htmlType="button">
                                        Select File
                                    </Button>
                                </div>
                            </FileUpload>
                        </CustomForm>

                        <div className="claim-ce-file-upload-actions">
                            <Button
                                color="default"
                                variant="outlined"
                                onClick={() => setShowFileUploadModal(false)}
                                className="claim-ce-file-upload-cancel"
                            >
                                Cancel
                            </Button>
                            <Button
                                color="primary"
                                variant="solid"
                                htmlType="submit"
                                form="file-upload-form"
                                className="claim-ce-file-upload-submit"
                                disabled={!newUploadedFile}
                            >
                                Upload
                            </Button>
                        </div>
                    </div>
                </Modal>
            )}
        </Content>
    );
};

export default CreateEstimatePage;
