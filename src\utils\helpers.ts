export const hasLowercaseLetter = (input: string): boolean => {
    return /[a-z]/.test(input);
};

export const hasUppercaseLetter = (input: string): boolean => {
    return /[A-Z]/.test(input);
};

export const hasNumber = (input: string): boolean => {
    return /\d/.test(input);
};

export const hasSymbol = (input: string): boolean => {
    return /[~`!@#$%^&*()\-_+={}[\]|\\;:"<>,./?]/.test(input);
};

export const hasEightLetterMin = (input: string): boolean => {
    return input.length >= 8;
};

export const formatCurrency = (amount: number, decimals?: number): string => {
    return amount.toLocaleString('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: decimals,
    });
};

export const formatNumberOutput = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(num);
};
