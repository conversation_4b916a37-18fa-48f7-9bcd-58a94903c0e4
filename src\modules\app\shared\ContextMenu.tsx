import { MenuProps, Tooltip } from 'antd';
import { Dropdown } from 'antd';
import classNames from 'classnames';
import React, { ReactNode } from 'react';
import { MenuItemType } from 'antd/es/menu/interface';
import Dots from './svg/Dots.tsx';

interface Props {
    action?: React.ReactNode;
    items: MenuProps['items'];
    onItemClick: (item: MenuItemType | undefined) => void;
    className?: string;
    overlayClassName?: string;
    toolTipContent?: ReactNode;
    hasTooltip?: boolean;
}

const ContextMenu: React.FC<Props> = ({
    action,
    items,
    onItemClick,
    className,
    overlayClassName,
    toolTipContent = 'More options',
    hasTooltip = true,
}) => {

    const onClick = (item: MenuItemType | undefined) => {
        onItemClick(item);
    };

    return (
        <Dropdown
            menu={{ items, onClick }}
            trigger={['click']}
            className={classNames('contextmenu', className)}
            overlayClassName={classNames('contextmenu-overlay', overlayClassName)}
        >
            <div>
                {
                    hasTooltip &&
                    <Tooltip
                        title={hasTooltip ? toolTipContent : undefined}
                        overlayClassName="contextmenu-tooltip"
                        overlayInnerStyle={{ backgroundColor: '$003049' }}
                    />
                }
                
                {
                    action ? action : <Dots className="icon-regular cursor" />
                }
            </div>
        </Dropdown>
    );
};

export default ContextMenu;
