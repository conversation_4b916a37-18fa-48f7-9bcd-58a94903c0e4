import React from 'react';
import { ReactComponent as IconFolder } from '../../../../assets/icons/icon-folder.svg';
import NavigationItem from './NavigationItem';
import StatusDot from '../../shared/StatusDot.tsx';
import { ClaimStatus } from '../../../../store/claims/types/Claim.ts';

const Navigation: React.FC = () => {

    return (
        <div className="navigation">
            <div className="navigation-group">
                <div className="navigation-group-label">
                    <NavigationItem
                        itemType="static"
                        className="navigation-item"
                        label="My projects"
                        iconBefore={<IconFolder />}
                    />
                </div>
                
                <div className="navigation-group-content">
                    <NavigationItem
                        className="navigation-item"
                        label="All projects"
                        link="/claims/all"
                        end={false}
                    />
                    <NavigationItem
                        className="navigation-item"
                        label="New"
                        link="/claims/new"
                        iconBefore={<StatusDot status={ClaimStatus.NEW} />}
                    />
                    <NavigationItem
                        className="navigation-item"
                        label="Pending"
                        link="/claims/pending"
                        iconBefore={<StatusDot status={ClaimStatus.PENDING} />}
                    />
                    <NavigationItem
                        className="navigation-item"
                        label="Scheduling"
                        link="/claims/scheduling"
                        iconBefore={<StatusDot status={ClaimStatus.SCHEDULING} />}
                    />
                    <NavigationItem
                        className="navigation-item"
                        label="Storage"
                        link="/claims/storage"
                        iconBefore={<StatusDot status={ClaimStatus.STORAGE} />}
                    />
                    <NavigationItem
                        className="navigation-item"
                        label="Closed"
                        link="/claims/closed"
                        iconBefore={<StatusDot status={ClaimStatus.CLOSED} />}
                    />
                    <NavigationItem
                        className="navigation-item"
                        label="Closed Lost"
                        link="/claims/closed-lost"
                        iconBefore={<StatusDot status={ClaimStatus.CLOSED_LOST} />}
                    />
                </div>
            </div>
        </div>
    );
};

export default Navigation;
