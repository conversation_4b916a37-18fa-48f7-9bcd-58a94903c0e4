import React, { useState } from "react";
import Modal from '../../app/shared/Modal.tsx';
import { Form } from 'antd';
import FlashMessages from '../../app/shared/FlashMessages.tsx';
import CustomForm from '../../app/shared/form/Form.tsx';
import InputField from '../../app/shared/form/InputField.tsx';
import CheckboxField from '../../app/shared/form/CheckboxField.tsx';
import Button from '../../app/shared/Button.tsx';
import { ReactComponent as IconCopy } from '../../../assets/icons/icon-copy.svg';
import { ReactComponent as IconLink } from '../../../assets/icons/icon-link-external.svg';
import { useClaimsContext } from '../ClaimsWrapper.tsx';
import { useClaimStore } from '../../../store';
import { Claim } from '../../../store/claims/types/Claim.ts';
import { getCompanyCamUrl } from '../../../utils/getCompanyCamUrl.ts';


interface Props {
    show: boolean;
    onClose: VoidFunction;
}


const AddCompanyCamModal: React.FC<Props> = ({ show, onClose }) => {
    const [form] = Form.useForm();
    const { currentClaim, setCurrentClaim } = useClaimsContext();
    const { updateClaim } = useClaimStore();
    const checkbox = Form.useWatch('images_uploaded', form);
    const [submitting, setSubmitting] = useState<boolean>(false);
    
    const handleCopy = (value?: string) => {
        if (!value) {
            return;
        }
        navigator.clipboard.writeText(value);
        FlashMessages.success("Copied!")
    };
    
    const onSubmit = (fields: any) => {
        setSubmitting(true);
        if (!currentClaim?.id) {
            return;
        }
        updateClaim(currentClaim.id, { images_uploaded: fields.images_uploaded }).then((claim: Claim) => {
            setCurrentClaim(claim);
            FlashMessages.success('Success!');
            onClose();
        }).finally(() => {
            setSubmitting(false);
        })
    }
    
    return (
        <Modal
            className="claim-modal-acw"
            show={show}
            onClose={onClose}
            title="Add CompanyCam Walkthrough"
            description="Use the link below to upload or view images for this project. If all photos are uploaded, click “Save & Continue”"
        >
            
            <CustomForm
                form={form}
                onSubmit={onSubmit}
            >
                <InputField
                    initialValue={getCompanyCamUrl(currentClaim)}
                    name="link"
                    inputProps={{
                        suffix: <div className="claim-modal-acw-actions">
                            <IconCopy onClick={() => handleCopy(getCompanyCamUrl(currentClaim))}
                                      className="icon-regular cursor"/>
                            <span className="separator"></span>
                            <a
                                href={getCompanyCamUrl(currentClaim)}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <IconLink className="icon-regular cursor dark"/>
                            </a>
                        </div>
                    }}
                    disabled={true}
                />
                
                <hr/>
                
                <CheckboxField
                    fontSize="default"
                    rules={[
                        { required: true, message: "Confirm that the images are uploaded" },
                    ]}
                    valuePropName="checked"
                    name="images_uploaded"
                    label="I confirm images are uploaded"
                />
                
                <Button
                    className="submit"
                    disabled={submitting || !checkbox}
                    color="primary"
                    variant="solid"
                    wide={true}
                >
                    Save & Continue
                </Button>
            </CustomForm>
            
            <div className="align-center margin-top-32 font-small">Have a link problem? <a href="mailto:<EMAIL>" rel="noopener noreferrer">Report</a></div>
        </Modal>
    );
};

export default AddCompanyCamModal;
