export type PaginationMeta = {
    current_page: number;
    per_page: number;
    total: number;
};

export type PaginationFields = {
    search_term?: string;
    order_by?: string;
    direction?: string;
    page?: number;
    limit?: number;
}

export interface ParamsField {
    field: string,
    operator: string,
    value: string
}

type Transition<T> = {
    loading: boolean;
    loaded: boolean;
    value: T;
    pageCount?: number;
};

export interface FileUrlData {
    url: string
    presigned_url: string
    presigned_url_expires_at: string
}

export interface Resource<T> {
    value: T;
    meta?: any;
    loading: boolean;
    loaded: boolean;
}

export const transition = {
    reset<T>(value: T, meta?: any): Resource<T> {
        return {
            loading: false,
            loaded: false,
            value,
            meta,
        };
    },
    loading<T>(value: T): Resource<T> {
        return {
            loading: true,
            loaded: false,
            value,
        };
    },
    loaded<T>(value: T, meta?: any): Resource<T> {
        return {
            loading: false,
            loaded: true,
            value,
            meta,
        };
    },
    clone<T, TResult>(transition: Transition<T>, value: TResult): Transition<TResult> {
        return {
            ...transition,
            value,
        };
    },
};

export const DEFAULT_PER_PAGE = 10;
