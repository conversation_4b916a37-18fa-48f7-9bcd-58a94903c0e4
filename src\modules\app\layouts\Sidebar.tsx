import React from 'react';
import { useNavigate } from 'react-router-dom';
import classNames from 'classnames';
import Logo from '../../../assets/sps-logo.png';
import {ReactComponent as IconLogout} from '../../../assets/icons/icon-logout.svg';
import { ReactComponent as IconPhone } from '../../../assets/icons/icon-phone.svg';
import { useAuthStore } from '../../../store';
import Button, { ButtonVariation } from '../shared/Button.tsx';

interface Props {
    children: React.ReactNode;
}

const Sidebar: React.FC<Props> = ({ children }) => {
    const { user, logout } = useAuthStore();
    const navigate = useNavigate();
    
    return (
        <div
            className={classNames({
                sidebar: true,
            })}
        >
            <div className="sidebar-logo">
                <img
                    src={Logo}
                    alt="Safe Pack & Store logo"
                    className="sidebar-logo-image"
                    onClick={() => {
                        navigate('/');
                    }}
                />
            </div>
            <div className="sidebar-content">{children}</div>
            <div className="sidebar-bottom">
                <a className="sidebar-bottom-group cursor" href='tel:************' rel="noopener noreferrer">
                    <div className="sidebar-bottom-group-meta">
                        <div className="title">Support</div>
                        <div className="label">************ ext 3</div>
                    </div>
                    <Button
                        icon={<IconPhone className="icon-regular"/>}
                        color="default"
                        variant="link"
                        variation={ButtonVariation.LINK}
                    />
                </a>
                
                <div className="sidebar-bottom-group">
                    <div className="sidebar-bottom-group-meta cursor" onClick={() => navigate('/profile')}>
                        <div className="title">{user?.name}</div>
                        <div className="label">{user?.email}</div>
                    </div>
                    <Button
                        icon={<IconLogout className="icon-regular"/>}
                        color="default"
                        variant="link"
                        variation={ButtonVariation.LINK}
                        onClick={() => logout()}
                    />
                </div>
            </div>
        </div>
    );
};

export default Sidebar;
