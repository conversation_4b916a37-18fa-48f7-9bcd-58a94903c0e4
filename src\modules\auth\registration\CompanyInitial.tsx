import { Col, Form, Row, Spin } from 'antd';
import Button, { ButtonSize, ButtonVariation } from '../../app/shared/Button.tsx';
import CustomForm from '../../app/shared/form/Form.tsx';
import React, { useCallback, useEffect, useState } from 'react';
import InputField from '../../app/shared/form/InputField.tsx';
import Arrow from '../../app/shared/svg/Arrow.tsx';
import SelectField, { SelectFormOption } from '../../app/shared/form/SelectField.tsx';
import { InvitationCompanyFormData, InvitationStep } from '../../../store/invitation/types/InvitationData.ts';
import { useInvitationStore } from '../../../store';
import debounce from "just-debounce-it";
import FlashMessages from '../../app/shared/FlashMessages.tsx';


interface Props {
    onStepChange: (step?: InvitationStep) => void;
    token: string;
}

const CompanyInitial: React.FC<Props> = ({ onStepChange, token }) => {
    const [form] = Form.useForm();
    const { invitationCompanyData, setCompanyDetailsStep, getBusinessContacts } = useInvitationStore();
    const [businessContactsOptions, setBusinessContactsOptions] = useState<SelectFormOption[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [isLoading, setIsLoading] = useState(false);
    const [submitting, setSubmitting] = useState<boolean>(false);
    // TODO update if total is bigger then limit
    const limit = 100;
    
    const onFinish = async (values: Partial<InvitationCompanyFormData>) => {
        if (typeof values.business_point_of_contact === 'object') {
            values.business_point_of_contact = values.business_point_of_contact.value
        }
        setSubmitting(true);
        try {
            await form.validateFields();
            await setCompanyDetailsStep(token, values, InvitationStep.STEP_1);
            onStepChange(InvitationStep.STEP_2)

        } catch (e: any) {
            const errors = e.response.data.errors;
            if (errors) {
                const fieldErrors = Object.keys(errors).map((field) => ({
                    name: field,
                    errors: errors[field],
                }));
                form.setFields(fieldErrors);
            }
            const message = e.response?.data?.message ? e.response?.data?.message : e.response?.data
            e.response?.data?.message && FlashMessages.error(message);
        } finally {
            setSubmitting(false);
        }
    };
    
    useEffect(() => {
        form.setFieldValue('company_name', invitationCompanyData?.form.company_name);
        form.setFieldValue('business_phone', invitationCompanyData?.form.business_phone);
        form.setFieldValue('business_address', invitationCompanyData?.form.business_address);
        form.setFieldValue('business_email', invitationCompanyData?.form.business_email);
        form.setFieldValue('business_license_number', invitationCompanyData?.form.business_license_number);
        form.setFieldValue('business_point_of_contact', invitationCompanyData?.form.business_point_of_contact);
        form.setFieldValue('business_city', invitationCompanyData?.form.business_city);
        form.setFieldValue('business_zip', invitationCompanyData?.form.business_zip);
    }, [invitationCompanyData]);
    
    const debouncedGetBusinessContacts = debounce(
        (page: number, search?: string) => {
            getBusinessContacts(token, { limit: limit, page, search_term: search || undefined })
                .then((r) => {
                    setBusinessContactsOptions(
                        r.data.map((item) => ({
                            value: item.id,
                            label: item.name,
                        }))
                    );
                    setCurrentPage(r.meta.current_page);
                })
                .finally(() => setIsLoading(false));
        },
        700
    );
    
    const handleGetBusinessPOC = useCallback(
        (search: string) => {
            setIsLoading(true);
            debouncedGetBusinessContacts(currentPage, search);
        },
        [currentPage, debouncedGetBusinessContacts]
    );
    
    useEffect(() => {
        debouncedGetBusinessContacts(currentPage);
    }, [currentPage]);
    
    return (
        <div>
            <div className="auth-register-top auth-register-group">
                <h1 className="heading h2">Tell us more about your company</h1>
                <p className="auth-register-top-text">This is initial information about your company.
                    You can change it anytime.</p>
            </div>
            <CustomForm
                form={form}
                onSubmit={onFinish}
            >
                <div className="auth-register-group">
                    <InputField
                        fontSize="medium"
                        fieldSize="medium"
                        name="company_name"
                        label="Company Name"
                        placeholder="Company Title"
                        className="auth-form-item"
                        disabled={true}
                    />
                    
                    <SelectField
                        label="Business Point of Contact:"
                        className="auth-form-item"
                        fontSize="medium"
                        selectProps={{
                            placeholder: 'Chose Contact',
                            showSearch: true,
                            optionFilterProp: "label",
                            loading: isLoading,
                            notFoundContent: isLoading ? <Spin size="small" /> : "No contacts found",
                            onSearch: (value) => handleGetBusinessPOC(value)
                        }}
                        rules={[
                            { required: true, message: "Business Point of Contact is required" },
                        ]}
                        name="business_point_of_contact"
                        options={businessContactsOptions}
                    />
                    
                    <InputField
                        fontSize="medium"
                        fieldSize="medium"
                        name="business_phone"
                        label="Business Phone:"
                        rules={[
                            { required: true, message: "Business phone is required" },
                        ]}
                        placeholder="Placeholder"
                        className="auth-form-item"
                    />
                    
                    <InputField
                        fontSize="medium"
                        fieldSize="medium"
                        name="business_email"
                        label="Business email:"
                        rules={[
                            { required: true, message: "Email address is required" },
                            { type: 'email', message: 'Please enter a valid email address.' }
                        ]}
                        type="email"
                        placeholder="<EMAIL>"
                        className="auth-form-item"
                    />
                    
                    <InputField
                        fontSize="medium"
                        fieldSize="medium"
                        name="business_address"
                        label="Business Address:"
                        rules={[
                            { required: true, message: "Business address is required" },
                        ]}
                        placeholder="Placeholder"
                        className="auth-form-item"
                    />
                    
                    <Row gutter={24}>
                        <Col className="gutter-row" span={16}>
                            <InputField
                                fontSize="medium"
                                fieldSize="medium"
                                name="business_city"
                                label="City"
                                rules={[
                                    { required: true, message: "City is required" },
                                ]}
                                placeholder="City"
                                className="auth-form-item"
                            />
                        </Col>
                        <Col className="gutter-row" span={8}>
                            <InputField
                                fontSize="medium"
                                fieldSize="medium"
                                name="business_zip"
                                label="ZIP"
                                rules={[
                                    { required: true, message: "Zip is required" },
                                ]}
                                type="number"
                                inputProps={{
                                    onWheel: (e) => e.currentTarget.blur()
                                }}
                                placeholder="Code"
                                className="auth-form-item"
                            />
                        </Col>
                    </Row>
                    
                    <InputField
                        fontSize="medium"
                        fieldSize="medium"
                        name="business_license_number"
                        label="Business License Number:"
                        rules={[
                            { required: true, message: "Business license number is required" },
                        ]}
                        placeholder="Placeholder"
                        className="auth-form-item"
                    />
                
                </div>
                
                
                <div className="auth-register-actions auth-register-group">
                    <Button
                        className="auth-register-actions-link"
                        color="default"
                        icon={<Arrow className="icon-regular" direction="left"/>}
                        iconPosition="start"
                        htmlType="button"
                        onClick={() => onStepChange(undefined)}
                        variant="link"
                        variation={ButtonVariation.LINK_SEMIBOLD}
                    >
                        Back
                    </Button>
                    <Button
                        disabled={submitting}
                        className="auth-register-actions-button"
                        buttonSize={ButtonSize.BIG}
                        type="primary"
                    >
                        Save and continue
                    </Button>
                </div>
            </CustomForm>
        </div>
    );
};

export default CompanyInitial;
