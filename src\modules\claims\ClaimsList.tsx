import withAuth from '../../hooks/withAuth.tsx';
import Content from '../app/layouts/content/Content.tsx';
import { ReactComponent as IconFolder } from '../../assets/icons/icon-folder.svg';
import { ColumnsType } from 'antd/es/table';
import Button, { ButtonSize } from '../app/shared/Button.tsx';
import { Claim } from '../../store/claims/types/Claim.ts';
import DataGrid from '../app/shared/DataGrid.tsx';
import ClaimStatusTag from '../app/shared/ClaimStatusTag.tsx';
import { getClaimStatusFromParam, useClaimsContext } from './ClaimsWrapper.tsx';
import { useNavigate, useParams } from 'react-router-dom';
import { useClaimStore } from '../../store';
import { useCallback, useEffect, useState } from 'react';
import { Pagination } from 'antd';
import { DEFAULT_PER_PAGE } from '../../@types';
import { format } from 'date-fns';
import { DATE_FORMAT_MONTHNAME } from '../../common/dateFormats.ts';
import { TablePaginationConfig } from 'antd/lib';
import { FilterValue, SorterResult } from 'antd/es/table/interface';
import Arrow from '../app/shared/svg/Arrow.tsx';

const ClaimsList = () => {
    const navigation = useNavigate();
    const params = useParams();
    const { type } = useClaimsContext();
    const { getClaims, claims, unloadClaims } = useClaimStore();
    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState<number>(1);
    
    const handleGetClaims = useCallback((type: string, order_by?: string, direction?: string) => {
        getClaims({
            limit: DEFAULT_PER_PAGE,
            page: currentPage,
            status: getClaimStatusFromParam(type),
            direction: direction ? direction : undefined,
            order_by: order_by ? order_by : undefined,
        }).then((r) => {
            setTotal(r.meta.total);
            setCurrentPage(r.meta.current_page)
        });
    }, [currentPage, getClaims])
    
    useEffect(() => {
        if (params.type !== type) {
            return;
        }
        type && handleGetClaims(type)
    }, [handleGetClaims, params.type, type]);
    
    const handleTableChange = (sorter: SorterResult<Claim> | SorterResult<Claim>[]) => {
        if (Array.isArray(sorter)) {
            return;
        }
        if (sorter.columnKey === 'submitted_date') {
            let direction: string | undefined;
            
            switch (sorter.order) {
                case 'descend':
                    direction = 'desc';
                    break;
                case 'ascend':
                    direction = 'asc';
                    break;
                default:
                    direction = undefined;
            }
            
            handleGetClaims(type, sorter.columnKey, direction)
        }
    };
    
    const columns: ColumnsType<Claim> = [
        {
            title: 'Name',
            dataIndex: 'claim_name'
        },
        {
            title: 'Job Number',
            dataIndex: 'job_number'
        },
        {
            title: <div className="head">
                <span className="head-title">Submitted date</span>
                <div className="head-sort">
                    <Arrow direction="down" className="head-sort-item desc"/>
                    <Arrow direction="up" className="head-sort-item asc"/>
                </div>
            </div>,
            showSorterTooltip: false,
            sorter: true,
            key: 'submitted_date',
            render: (claim: Claim) => {
                if (!claim.submitted_date) {
                    return;
                }
                
                return (
                    <div>{format(claim.submitted_date, DATE_FORMAT_MONTHNAME)}</div>
                )
            }
        },
        {
            title: 'Status',
            render: (claim: Claim) => {
                return (
                    <ClaimStatusTag status={claim.status}/>
                )
            }
        }
    ];
    
    useEffect(() => {
        return () => {
            unloadClaims();
        }
    }, [unloadClaims]);
    
    return (
        <Content
            breadcrumbItems={[
                {
                    href: `/claims/${type}`,
                    title: (
                        <>
                            <IconFolder className="icon-regular"/>
                            <span>{getClaimStatusFromParam(type) ? getClaimStatusFromParam(type) : 'All Projects'}</span>
                        </>
                    ),
                }
            ]}
            headerSubtitle="Explore and manage all your projects in one place."
            headerTitle="My projects"
            // headerActions={
            //     <>
            //         <Button icon={<IconPlus className="icon-regular"/>}>Secondary</Button>
            //         <Button icon={<IconPlus className="icon-regular"/>} variant="solid" color="primary">Primary
            //             Button</Button>
            //     </>
            // }
        >
            <DataGrid<Claim>
                list={claims.value}
                loading={claims.loading}
                columns={columns}
                title={
                    <>
                        <h5 className="table-header-title">{getClaimStatusFromParam(type) ? getClaimStatusFromParam(type) : 'All Projects'}</h5>
                        <div className="table-header-count">{total} total</div>
                    </>
                }
                footer={
                    Math.ceil(total / DEFAULT_PER_PAGE) > 1 &&
                        <>
                            <div
                                className="table-footer-meta">Page {currentPage} of {Math.ceil(total / DEFAULT_PER_PAGE)}</div>
                            <Pagination
                                className="table-footer-actions"
                                itemRender={(_, type) => {
                                    if (type === "prev") {
                                        return <Button variant="outlined" size="small"
                                                       buttonSize={ButtonSize.SMALL}>Previous</Button>;
                                    }
                                    if (type === "next") {
                                        return <Button variant="outlined" size="small"
                                                       buttonSize={ButtonSize.SMALL}>Next</Button>;
                                    }
                                    
                                    return;
                                }}
                                defaultCurrent={currentPage} total={total} pageSize={DEFAULT_PER_PAGE}
                                onChange={(value) => setCurrentPage(value)}
                            />
                        </>
                }
                props={{
                    rowClassName: 'cursor',
                    onRow: (record) => ({
                        onClick: () => navigation(`/claims/${type}/${record?.id}`),
                    }),
                    onChange: (
                        _: TablePaginationConfig,
                        __: Record<string, FilterValue | null>,
                        sorter: SorterResult<Claim> | SorterResult<Claim>[]
                    ) => handleTableChange(sorter),
                }}
            />
        </Content>
    );
};

export default withAuth(ClaimsList);
