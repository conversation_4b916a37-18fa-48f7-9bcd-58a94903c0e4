import CustomForm from '../app/shared/form/Form.tsx';
import Button, { ButtonSize, ButtonVariation } from '../app/shared/Button.tsx';
import AuthLayout from '../app/layouts/AuthLayout.tsx';
import { Form, FormInstance } from 'antd';
import { RuleObject } from 'antd/es/form';
import PasswordField from '../app/shared/form/PasswordField.tsx';
import {
    hasEightLetterMin,
    hasLowercaseLetter,
    hasNumber,
    hasSymbol,
    hasUppercaseLetter
} from '../../utils/helpers.ts';
import PasswordRequirementsList from '../app/shared/form/PasswordRequirementsList.tsx';
import { useEffect, useState } from 'react';
import { useAuthStore } from '../../store';
import { useNavigate } from 'react-router-dom';
import FlashMessages from '../app/shared/FlashMessages.tsx';

const ResetPasswordPage = () => {
    const [form] = Form.useForm();
    const password = Form.useWatch('password', form);
    const passwordConfirm = Form.useWatch('password-confirm', form);
    const [canSubmit, setCanSubmit] = useState<boolean>(false);
    const [submitting, setSubmitting] = useState<boolean>(false);
    const [passwordIsSet, setPasswordIsSet] = useState<boolean>(false);
    const token = (new URLSearchParams(window.location.search)).get('token');
    const email = (new URLSearchParams(window.location.search)).get('email');
    const [visible, setVisible] = useState<boolean>(false);
    const {resetPassword} = useAuthStore();
    const navigate = useNavigate();
    
    const onFinish = async () => {
        if (!token || !email) {
            return;
        }
        setSubmitting(true);
        
        resetPassword({
            token: token,
            email: email,
            password: password
        }).then(() => {
            setPasswordIsSet(true);
        }).catch((e) => {
            const errors = e.response.data.errors;
            if (errors) {
                const fieldErrors = Object.keys(errors).map((field) => ({
                    name: field,
                    errors: errors[field],
                }));
                form.setFields(fieldErrors);
            }
            const message = e.response?.data?.message ? e.response?.data?.message : e.response?.data
            e.response?.data?.message && FlashMessages.error(message);
        }).finally(() => {
            setSubmitting(false);
        })
    };

    useEffect(() => {
        if (!visible) {
            return;
        }
        form
            .validateFields()
            .then(() => {
                setCanSubmit(true);
            })
            .catch(() => {
                setCanSubmit(false);
            });
    }, [passwordConfirm, password, form, visible]);

    useEffect(() => {
        if (!token || !email) {
            navigate('/auth/login');
            return;
        }
        setVisible(true);
    }, [email, navigate, token]);

    if (!visible) {
        return null;
    }

    return (
        <AuthLayout
            className="auth-np auth-bg"
            subtitle={
                passwordIsSet ?
                    "Your password has been reset!"
                    :
                    "Must be at least 8 characters"
            }
            title={
                passwordIsSet ?
                    "You are done!"
                    :
                    "Set new password"
            }
        >
            {
                !passwordIsSet &&
                <CustomForm
                    form={form}
                    onSubmit={onFinish}
                    className="auth-form"
                >
                    <PasswordField
                        fieldSize="medium"
                        fontSize="medium"
                        name="password"
                        placeholder="● ● ● ● ● ● ● ● ●"
                        rules={[
                            { required: true, message: "Password is required" },
                            {
                                validator: (_: RuleObject, value: string) => {
                                    if (!value) return Promise.resolve();
                                    const conditions = [
                                        hasLowercaseLetter(value),
                                        hasUppercaseLetter(value),
                                        hasNumber(value),
                                        hasSymbol(value),
                                        hasEightLetterMin(value),
                                    ];
                                    const allConditionsMet = conditions.every(Boolean);
                                    
                                    if (allConditionsMet) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(
                                        new Error("Password does not meet the requirements")
                                    );
                                },
                            },
                        ]}
                        label="New Password"
                    />

                    <PasswordRequirementsList inputValue={password}/>

                    <PasswordField
                        fieldSize="medium"
                        fontSize="medium"
                        className="auth-form-item big"
                        name="password-confirm"
                        placeholder="● ● ● ● ● ● ● ● ●"
                        label="Confirm New Password"
                        rules={[
                            {
                                required: true,
                                message: 'Please confirm your password!',
                            },
                            ({ getFieldValue }: { getFieldValue: FormInstance['getFieldValue'] }): RuleObject => ({
                                async validator(_: RuleObject, value: string) {
                                    if (!value || getFieldValue('password') === value) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error('Passwords don’t match.'));
                                },
                            }),
                        ]}
                    />

                    <Button
                        className="auth-button"
                        buttonSize={ButtonSize.BIG}
                        type="primary"
                        disabled={!canSubmit || submitting}
                    >
                        Set new password
                    </Button>

                    <Button
                        variation={ButtonVariation.LINK_SEMIBOLD}
                        buttonSize={ButtonSize.BIG}
                        htmlType="button"
                        onClick={() => navigate('/auth/login')}
                        variant="link"
                        color="default"
                        type="primary"
                        className="auth-button-back"
                    >
                        Back
                    </Button>
                </CustomForm>
            }
            
            {
                passwordIsSet &&
                <Button
                    className="auth-button"
                    type="primary"
                    onClick={() => navigate('/auth/login')}
                >
                    Login
                </Button>
            }
            
        </AuthLayout>
    );
};

export default ResetPasswordPage;
