import { EstimateCreateProduct, EstimateItem } from '../../../../store/claims/types/Claim.ts';

export const calculateTotalUnitPrice = (data: Record<string, EstimateCreateProduct[] | EstimateItem[]>): number => {
    return Object.values(data)
        .flat()
        .reduce((sum, item) => {
            const baseTotal = item.unit_price * item.quantity;
            // For Storage items, multiply by length if it exists
            // Check if item is EstimateItem (has product_family) and is Storage with length
            if ('product_family' in item && item.product_family === 'Storage' && 'length' in item && item.length) {
                return sum + (baseTotal * Number(item.length));
            }
            return sum + baseTotal;
        }, 0);
};

export const PriceBookSortingGroups = ['Packing', 'Pack Out', 'Storage', 'Pack Back', 'Unpacking']
