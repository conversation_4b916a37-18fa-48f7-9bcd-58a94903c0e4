import React from 'react';
import { Form } from 'antd';
import { FormProps } from 'antd/lib/form/Form';
import classNames from 'classnames';

interface Props extends FormProps {
    children: React.ReactNode;
    onSubmit: (values: any) => void;
}

const CustomForm: React.FC<Props> = ({ children, onSubmit, className, ...rest }) => {
    
    const onFinish = (values: any) => {
        onSubmit(values);
    };
    
    return (
        <Form.Provider>
            <Form
                className={classNames('form', className)}
                onFinish={onFinish}
                layout="vertical"
                requiredMark={false}
                {...rest}
            >
                {children}
            </Form>
        </Form.Provider>
    );
};

export default CustomForm;