import React, { useState } from 'react';
import Content from '../../../app/layouts/content/Content.tsx';
import { EstimateItem } from '../../../../store/claims/types/Claim.ts';
import { Col, Row } from 'antd';
import { formatCurrency } from '../../../../utils/helpers.ts';
import Button, { ButtonVariation } from '../../../app/shared/Button.tsx';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import ClaimStatusTag from '../../../app/shared/ClaimStatusTag.tsx';
import Modal, { ModalSize } from '../../../app/shared/Modal.tsx';
import FileViewer from '../../../app/shared/FileViewer.tsx';
import FileUpload from '../../../app/shared/form/FileUpload.tsx';
import CustomForm from '../../../app/shared/form/Form.tsx';
import { ReactComponent as IconUpload } from '../../../../assets/icons/icon-upload-cloud.svg';
import { useClaimStore, useFileStore } from '../../../../store';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';
import classNames from 'classnames';

interface Props {
    items: EstimateItem[];
    onSubmitForApproval: VoidFunction;
}

const EstimateSummaryPage: React.FC<Props> = ({ items, onSubmitForApproval }) => {
    const navigate = useNavigate();
    const params = useParams();
    const location = useLocation();

    const { currentClaim } = useClaimsContext();
    const { getClaimEstimateDocumentLink } = useClaimStore();
    const { uploadFileS3 } = useFileStore();

    // State for file handling and modals
    const [infoExpanded, setInfoExpanded] = useState<boolean>(false);
    const [showPreviewModal, setShowPreviewModal] = useState<boolean>(false);
    const [showFileUploadModal, setShowFileUploadModal] = useState<boolean>(false);
    const [newUploadedFile, setNewUploadedFile] = useState<File | null>(null);
    const [currentFile, setCurrentFile] = useState<File | string>('');

    // Get current file from location state
    React.useEffect(() => {
        if (location.state) {
            const { currentFile: stateCurrentFile } = location.state as any;
            if (stateCurrentFile) {
                setCurrentFile(stateCurrentFile);
            }
        }
    }, [location.state]);

    const onGoBack = () => {
        // Get the form data from location state to preserve it when going back
        const { formData, newUploadedFile, currentFile, estimate } = location.state as any;

        navigate(`/claims/${params.type}/${params.id}/estimate/create`, {
            state: {
                formData,
                newUploadedFile: newUploadedFile || null,
                currentFile: currentFile || '',
                estimate,
                preserveFormData: true,
            },
        });
    };

    // File handling functions
    const handlePreviewFile = () => {
        setShowPreviewModal(true);
    };

    const handleChangeFile = () => {
        setShowFileUploadModal(true);
    };

    const handleFileUploadSubmit = () => {
        if (!newUploadedFile) {
            FlashMessages.error('Please select a file to upload.');
            return;
        }
        console.log('Form submitted, upload process will handle file replacement');
    };

    const handleFileSelected = (file: File) => {
        setNewUploadedFile(file);
    };

    const handleUploadFinish = async (uploadSuccess: boolean) => {
        if (uploadSuccess && newUploadedFile && currentClaim?.id) {
            try {
                console.log('Upload completed for file:', newUploadedFile.name);

                // Upload the file to S3 via API
                const documentLinkObject = await getClaimEstimateDocumentLink(
                    currentClaim.id,
                    newUploadedFile.name,
                );
                await uploadFileS3(newUploadedFile, documentLinkObject.presigned_url);

                // Update the local current file state for immediate preview
                setCurrentFile(newUploadedFile);

                // Show success message
                FlashMessages.success(
                    `File "${newUploadedFile.name}" has been successfully uploaded and replaced!`,
                );

                // Close the modal and reset state
                setShowFileUploadModal(false);
                setNewUploadedFile(null);
            } catch (error) {
                console.error('Error replacing file:', error);
                FlashMessages.error('Failed to replace the file. Please try again.');
            }
        } else {
            console.log('Upload failed or no file selected');
        }
    };

    const groupedItems = items.reduce<Record<string, EstimateItem[]>>((acc, product) => {
        acc[product.product_family] = acc[product.product_family] || [];
        acc[product.product_family].push(product);
        return acc;
    }, {});

    const calculateSubtotal = (items: EstimateItem[]) => {
        return items.reduce((sum, item) => {
            const baseTotal = item.unit_price * item.quantity;
            // For Storage items, multiply by length if it exists
            if (item.product_family === 'Storage' && (item as any).length) {
                return sum + baseTotal * (item as any).length;
            }
            return sum + baseTotal;
        }, 0);
    };

    const calculateTotal = () => {
        return Object.values(groupedItems).reduce(
            (sum, items) => sum + calculateSubtotal(items),
            0,
        );
    };

    const renderServiceSection = (category: string, items: EstimateItem[]) => {
        const isStorage = category === 'Storage';

        return (
            <div key={category} className="estimate-summary-section">
                <div className="estimate-summary-section-header">{category}</div>
                <div className="estimate-summary-section-content">
                    <div className="estimate-summary-section-table">
                        <Row className="estimate-summary-header-row">
                            <Col span={6}>Service Title</Col>
                            <Col span={4}>{isStorage ? 'Qty' : 'Hours'}</Col>
                            <Col span={4}>Rate</Col>
                            {isStorage && <Col span={4}>Length</Col>}
                            <Col span={isStorage ? 6 : 10} className="end">
                                Total
                            </Col>
                        </Row>
                        {items.map((item, index) => (
                            <Row key={index} className="estimate-summary-item-row">
                                <Col span={6}>{item.product_name}</Col>
                                <Col span={4}>
                                    {isStorage ? item.quantity : `${item.quantity} hours`}
                                </Col>
                                <Col span={4}>{formatCurrency(item.unit_price)}</Col>
                                {isStorage && <Col span={4}>{(item as any).length || 1}</Col>}
                                <Col span={isStorage ? 6 : 10} className="end">
                                    {formatCurrency(
                                        (() => {
                                            const baseTotal = item.unit_price * item.quantity;
                                            // For Storage items, multiply by length if it exists
                                            if (
                                                item.product_family === 'Storage' &&
                                                (item as any).length
                                            ) {
                                                return baseTotal * (item as any).length;
                                            }
                                            return baseTotal;
                                        })(),
                                    )}
                                </Col>
                            </Row>
                        ))}
                    </div>
                </div>
                <div className="estimate-summary-subtotal">
                    <span className="estimate-summary-subtotal-label">{category} Subtotal</span>
                    <span className="estimate-summary-subtotal-value">
                        {formatCurrency(calculateSubtotal(items))}
                    </span>
                </div>
            </div>
        );
    };

    const handleBreadcrumbNavigation = () => {
        // Get the form data from location state to preserve it when navigating via breadcrumb
        const { formData, newUploadedFile, currentFile, estimate } = location.state as any;

        navigate(`/claims/${params.type}/${params.id}/estimate/create`, {
            state: {
                formData,
                newUploadedFile,
                currentFile,
                estimate,
                preserveFormData: true,
            },
        });
    };

    return (
        <Content
            headerTitle="Estimate Summary"
            headerSubtitle="Please verify your estimation before sending for Approval."
            breadcrumbItems={[
                { title: 'Projects', href: `/claims/${params.type}` },
                {
                    title: currentClaim?.claim_name || 'Claim',
                    href: `/claims/${params.type}/${params.id}`,
                },
                {
                    title: 'Add Services',
                    onClick: handleBreadcrumbNavigation,
                },
                { title: 'Estimate Summary' },
            ]}
        >
            <div
                className="claim-ce"
                style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}
            >
                {/* Top content - same as CreateEstimatePage */}
                <div
                    className="claim-ce-top-section"
                    style={{
                        width: '100%',
                        background: '#f8f9fa',
                        padding: '20px',
                        borderRadius: '8px',
                        display: 'flex',
                        gap: '24px',
                        alignItems: 'flex-start',
                    }}
                >
                    {/* Left side - Claim header and info */}
                    <div style={{ flex: '1', minWidth: '0' }}>
                        <div className="claim-header" style={{ marginBottom: '24px' }}>
                            <h1 className="heading h3">{currentClaim?.claim_name}</h1>
                            <ClaimStatusTag status={currentClaim?.status} />
                        </div>

                        <div className="claim-ce-content">
                            <div className="claim-ce-info" style={{ marginBottom: '0px' }}>
                                <div
                                    className={classNames('claim-ce-info-inner', {
                                        expanded: infoExpanded,
                                    })}
                                    style={{ background: 'none', paddingTop: '0px' }}
                                >
                                    <h5 className="claim-ce-info-title">Create your estimate</h5>
                                    <div className="claim-ce-info-group">
                                        <p className="claim-ce-info-group-description">
                                            Now it's time to input your line items. Using our
                                            service page, select a service and enter the
                                            corresponding rate and quantity.
                                        </p>
                                        <div
                                            className="claim-ce-info-action"
                                            onClick={() => setInfoExpanded(!infoExpanded)}
                                            style={{
                                                marginTop: '8px',
                                                cursor: 'pointer',
                                                display: 'inline-block',
                                            }}
                                        >
                                            {infoExpanded ? 'Less' : 'Learn More'}
                                        </div>
                                    </div>

                                    {infoExpanded && (
                                        <>
                                            <div className="claim-ce-info-group">
                                                <h6 className="claim-ce-info-group-title">
                                                    Helpful Tips!
                                                </h6>
                                                <p className="claim-ce-info-group-description">
                                                    For materials, group them under one service and
                                                    input the total cost for all items from your
                                                    original estimate. This will streamline the
                                                    process and ensure accuracy. Pack Out = Move
                                                    Out. Pack Back = Move Back.
                                                </p>
                                            </div>
                                            <div className="claim-ce-info-group">
                                                <p className="claim-ce-info-group-description">
                                                    <b>Storage:</b> Select the type of storage
                                                    required (e.g., vault or lb) and enter the
                                                    estimated storage needed in Qty. Storage is
                                                    estimated for one month.
                                                </p>
                                            </div>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right side - Original estimate section */}
                    <div style={{ flex: '0 0 300px' }}>
                        <div
                            className="claim-ce-viewer-head"
                            style={{
                                padding: '0',
                                margin: '0',
                                borderTop: 'none',
                            }}
                        >
                            <h6 className="claim-ce-viewer-head-title">Original estimate</h6>

                            <div className="claim-ce-viewer-head-actions">
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handlePreviewFile}
                                    className="claim-ce-viewer-head-actions-preview"
                                >
                                    Preview
                                </Button>
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handleChangeFile}
                                    className="claim-ce-viewer-head-actions-change"
                                >
                                    Change
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main content - estimate summary */}
                <div className="estimate-summary-content" style={{ width: '100%' }}>
                    {Object.entries(groupedItems).map(([category, items]) =>
                        renderServiceSection(category, items),
                    )}

                    <div className="estimate-summary-total">
                        <span className="estimate-summary-total-label">Total Estimate</span>
                        <span className="estimate-summary-total-value">
                            {formatCurrency(calculateTotal())}
                        </span>
                    </div>
                </div>

                <div className="estimate-summary-actions">
                    <Button
                        color="default"
                        variant="outlined"
                        onClick={onGoBack}
                        className="estimate-summary-back-button"
                    >
                        GO BACK
                    </Button>
                    <Button
                        color="primary"
                        variant="solid"
                        onClick={onSubmitForApproval}
                        className="estimate-summary-submit-button"
                    >
                        SUBMIT FOR APPROVAL
                    </Button>
                </div>
            </div>

            {/* Preview Modal */}
            {showPreviewModal && (
                <Modal
                    show={showPreviewModal}
                    onClose={() => setShowPreviewModal(false)}
                    size={ModalSize.BIG}
                    title="Original Estimate Preview"
                    className="claim-ce-preview-modal"
                >
                    <div className="claim-ce-preview-content">
                        <FileViewer file={currentFile} />
                    </div>
                </Modal>
            )}

            {/* File Upload Modal */}
            {showFileUploadModal && (
                <Modal
                    show={showFileUploadModal}
                    onClose={() => setShowFileUploadModal(false)}
                    size={ModalSize.MEDIUM}
                    title="Change Original Estimate"
                    className="claim-ce-file-upload-modal"
                >
                    <div className="claim-ce-file-upload-content">
                        <CustomForm id="file-upload-form" onSubmit={handleFileUploadSubmit}>
                            <FileUpload
                                name="file"
                                label="Upload new estimate file"
                                rules={[{ required: true, message: 'Please upload a file!' }]}
                                selectedFile={handleFileSelected}
                                onFinish={handleUploadFinish}
                                maxFileSize={10}
                                showProgressBar={true}
                            >
                                <div className="claim-ce-upload" aria-label="File upload area">
                                    <IconUpload
                                        className="claim-ce-upload-icon"
                                        aria-label="Upload icon"
                                    />
                                    <div className="claim-ce-upload-description">
                                        <div className="claim-ce-upload-description-title">
                                            Select a file or drag and drop here
                                        </div>
                                        <div className="claim-ce-upload-description-label">
                                            (JPG, PNG or PDF, file size no more than 10MB)
                                        </div>
                                    </div>
                                    <Button size="large" htmlType="button">
                                        Select File
                                    </Button>
                                </div>
                            </FileUpload>
                        </CustomForm>

                        <div className="claim-ce-file-upload-actions">
                            <Button
                                color="default"
                                variant="outlined"
                                onClick={() => setShowFileUploadModal(false)}
                                className="claim-ce-file-upload-cancel"
                            >
                                Cancel
                            </Button>
                            <Button
                                color="primary"
                                variant="solid"
                                htmlType="submit"
                                form="file-upload-form"
                                className="claim-ce-file-upload-submit"
                                disabled={!newUploadedFile}
                            >
                                Upload
                            </Button>
                        </div>
                    </div>
                </Modal>
            )}
        </Content>
    );
};

export default EstimateSummaryPage;
