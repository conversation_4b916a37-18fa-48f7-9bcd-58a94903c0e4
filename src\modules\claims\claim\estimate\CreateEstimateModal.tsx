import React, { useEffect, useState } from 'react';
import Modal, { ModalSize } from '../../../app/shared/Modal.tsx';
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Col, Form, Row, Select, Input } from 'antd';
import Button, { ButtonVariation } from '../../../app/shared/Button.tsx';
import ClaimStatusTag from '../../../app/shared/ClaimStatusTag.tsx';
import {
    Estimate,
    EstimateCreateProduct,
    EstimateItem,
    PriceBookItem,
} from '../../../../store/claims/types/Claim.ts';
import { ReactComponent as IconPlus } from '../../../../assets/icons/icon-plus.svg';
import { ReactComponent as IconMinusFilled } from '../../../../assets/icons/icon-minus-rounded-fill.svg';
import { ReactComponent as IconUpload } from '../../../../assets/icons/icon-upload-cloud.svg';
import SelectField from '../../../app/shared/form/SelectField.tsx';
import { useClaimStore, useFileStore } from '../../../../store';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';
import { formatCurrency, formatNumberOutput } from '../../../../utils/helpers.ts';
import { PriceBookSortingGroups } from './utils.ts';
import classNames from 'classnames';
import InputField from '../../../app/shared/form/InputField.tsx';
import Loader from '../../../app/shared/Loader.tsx';
import { RuleObject } from 'antd/es/form';
import Dialog from '../../../app/shared/Dialog.tsx';
import EstimateSummaryModal from './EstimateSummaryModal.tsx';
import EstimateConfirmationModal from './EstimateConfirmationModal.tsx';
import FileViewer from '../../../app/shared/FileViewer.tsx';
import FileUpload from '../../../app/shared/form/FileUpload.tsx';

interface Props {
    onChangeFile?: (newFile: File) => void;
    onClose: VoidFunction;
    onSuccess: (estimate: Estimate) => void;
    show: boolean;
    uploadedFile: File | string;
    estimate?: Estimate;
}

const CreateEstimateModal: React.FC<Props> = ({
    estimate,
    show,
    onClose,
    onSuccess,
    uploadedFile,
}) => {
    const [form] = Form.useForm();
    const formValues = Form.useWatch([], form);
    const { getClaimPriceBook, setEstimate, getClaimEstimateDocumentLink } = useClaimStore();
    const { uploadFileS3 } = useFileStore();
    const { currentClaim } = useClaimsContext();
    const [groupedProducts, setGroupedProducts] = useState<Record<string, PriceBookItem[]>>({});
    const [canSubmit, setCanSubmit] = useState<boolean>(true);
    const [infoExpanded, setInfoExpanded] = useState<boolean>(false);
    const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
    const [priceBookLoading, setPriceBookLoading] = useState<boolean>(false);
    const [estimateItems, setEstimateItems] = useState<Record<string, EstimateItem[]>>({});
    const [groupSelectEnabled, setGroupSelectEnabled] = useState<Record<string, boolean>>({});
    const [selectedTab, setSelectedTab] = useState<string>('');
    const [showEstimateSummary, setShowEstimateSummary] = useState<boolean>(false);
    const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
    const [currentEstimateItems, setCurrentEstimateItems] = useState<EstimateItem[]>([]);
    const [tempFormValues, setTempFormValues] = useState<Record<string, any>>({});
    const [showPreviewModal, setShowPreviewModal] = useState<boolean>(false);
    const [showFileUploadModal, setShowFileUploadModal] = useState<boolean>(false);
    const [newUploadedFile, setNewUploadedFile] = useState<File | null>(null);
    const [currentFile, setCurrentFile] = useState<File | string>(uploadedFile);

    const groupNames = Object.keys(groupedProducts);

    // Update current file when uploadedFile prop changes
    useEffect(() => {
        setCurrentFile(uploadedFile);
    }, [uploadedFile]);

    useEffect(() => {
        if (!estimate?.items) {
            return;
        }
        setEstimateItems(
            estimate.items.reduce<Record<string, EstimateItem[]>>((acc, product) => {
                acc[product.product_family] = acc[product.product_family] || [];
                acc[product.product_family].push(product);
                return acc;
            }, {}),
        );
    }, []);

    const onSubmit = async (fields: any) => {
        if (!currentClaim?.id) {
            return;
        }
        setCanSubmit(false);
        try {
            let documentLink = estimate?.original_estimate || '';
            if (typeof uploadedFile === 'object') {
                const name = uploadedFile.name;
                const documentLinkObject = await getClaimEstimateDocumentLink(
                    currentClaim?.id,
                    name,
                );
                await uploadFileS3(uploadedFile, documentLinkObject.presigned_url);
                documentLink = documentLinkObject.url;
            }
            console.log('=== SUBMIT DEBUG ===');
            console.log('Raw fields data:', JSON.stringify(fields, null, 2));
            console.log('Object.keys(fields):', Object.keys(fields));
            console.log('Object.values(fields):', Object.values(fields));

            const products: EstimateCreateProduct[] = Object.values(
                fields,
            ).flat() as EstimateCreateProduct[];

            console.log('Flattened products for API:', JSON.stringify(products, null, 2));
            console.log('Total products count:', products.length);
            console.log('=== END SUBMIT DEBUG ===');

            const createdEstimate = await setEstimate(currentClaim?.id, {
                original_estimate: documentLink,
                line_item_description: currentClaim?.project_description || undefined,
                products: products,
            });
            onSuccess(createdEstimate);
        } catch (errors) {
            FlashMessages.error('Something went wrong, please try again!');
            console.error(errors);
        } finally {
            setCanSubmit(true);
        }
    };

    const handleEstimateSummary = () => {
        console.log('=== ESTIMATE SUMMARY DEBUG ===');
        console.log('Getting estimate summary for all tabs');
        console.log('Available group names:', groupNames);
        console.log('Current selectedTab:', selectedTab);

        // Get ALL form data from ALL tabs
        const allFormValues = form.getFieldsValue();
        console.log('All form values:', JSON.stringify(allFormValues, null, 2));

        // Ensure we have data for all groups by checking each one individually
        const completeFormData: Record<string, any> = {};
        groupNames.forEach((groupName) => {
            const groupValue = form.getFieldValue(groupName);
            if (groupValue && Array.isArray(groupValue) && groupValue.length > 0) {
                completeFormData[groupName] = groupValue;
                console.log(`${groupName} data (${groupValue.length} items):`, groupValue);
            } else {
                console.log(`${groupName} data: empty or undefined`);
            }
        });

        console.log('Complete form data:', JSON.stringify(completeFormData, null, 2));

        // Use the more complete dataset
        const dataToProcess =
            Object.keys(completeFormData).length > 0 ? completeFormData : allFormValues;
        console.log(
            'Using data source:',
            Object.keys(completeFormData).length > 0 ? 'completeFormData' : 'allFormValues',
        );

        processEstimateData(dataToProcess);
    };

    const processEstimateData = (formData: Record<string, any>) => {
        console.log('Processing estimate data:', JSON.stringify(formData, null, 2));

        // Convert to EstimateItem format
        const items: EstimateItem[] = [];

        // Process each category separately to maintain product_family information
        Object.entries(formData).forEach(([category, categoryItems]) => {
            console.log(`\n--- Processing category: ${category} ---`);
            console.log(`Category items:`, categoryItems);
            console.log(`Is array:`, Array.isArray(categoryItems));
            console.log(
                `Array length:`,
                Array.isArray(categoryItems) ? categoryItems.length : 'N/A',
            );

            if (Array.isArray(categoryItems)) {
                categoryItems.forEach((item: any, index: number) => {
                    console.log(`\n  Processing item ${index} in ${category}:`);
                    console.log(`    Raw item:`, JSON.stringify(item, null, 4));
                    console.log(`    Has product_id:`, !!item.product_id);
                    console.log(`    Has name:`, !!item.name);
                    console.log(
                        `    Condition check (product_id || name):`,
                        !!(item && (item.product_id || item.name)),
                    );

                    if (item && (item.product_id || item.name)) {
                        // Include items that have been properly added
                        const estimateItem: any = {
                            product_id: item.product_id || '',
                            quantity: Number(item.quantity) || 0,
                            unit_price: Number(item.unit_price) || 0,
                            product_name: item.name || '',
                            product_description: '',
                            product_family: category,
                        };

                        // Add length field for Storage items
                        if (category === 'Storage' && item.length) {
                            estimateItem.length = Number(item.length);
                        }

                        console.log(
                            `    ✅ ADDING estimate item:`,
                            JSON.stringify(estimateItem, null, 4),
                        );
                        items.push(estimateItem);
                    } else {
                        console.log(`    ❌ SKIPPING item (incomplete):`, item);
                    }
                });
            } else {
                console.log(`  ❌ Category ${category} is not an array or is empty`);
            }
            console.log(`--- End processing ${category} ---\n`);
        });

        console.log('Final estimate items for summary:', items);
        console.log('Total items found:', items.length);
        console.log('=== END ESTIMATE SUMMARY DEBUG ===');

        setCurrentEstimateItems(items);
        setShowEstimateSummary(true);
    };

    const handleConfirmationYes = () => {
        setShowConfirmationModal(false);
        handleEstimateSummary();
    };

    const handleConfirmationNo = () => {
        setShowConfirmationModal(false);
    };

    const handlePreviewFile = () => {
        setShowPreviewModal(true);
    };

    const handleChangeFile = () => {
        // Open the internal file upload modal for file replacement
        setShowFileUploadModal(true);
    };

    const handleFileUploadSubmit = () => {
        if (!newUploadedFile) {
            FlashMessages.error('Please select a file to upload.');
            return;
        }
        // The actual file replacement will be handled in handleUploadFinish
        // when the FileUpload component completes the upload process
        console.log('Form submitted, upload process will handle file replacement');
    };

    const handleFileSelected = (file: File) => {
        setNewUploadedFile(file);
    };

    const handleUploadFinish = (uploadSuccess: boolean) => {
        if (uploadSuccess && newUploadedFile) {
            // Upload completed successfully, now handle the file replacement
            try {
                console.log('Upload completed for file:', newUploadedFile.name);

                // Update the local current file state for immediate preview
                setCurrentFile(newUploadedFile);

                // NOTE: We don't call onChangeFile here because that would trigger
                // the parent to open the upload modal again. The file has been
                // successfully replaced locally and the preview will show the new file.

                // Show success message
                FlashMessages.success(
                    `File "${newUploadedFile.name}" has been successfully uploaded and replaced!`,
                );

                // Close the modal and reset state
                setShowFileUploadModal(false);
                setNewUploadedFile(null);
            } catch (error) {
                console.error('Error replacing file:', error);
                FlashMessages.error('Failed to replace the file. Please try again.');
            }
        } else if (!uploadSuccess) {
            // Upload failed
            FlashMessages.error('File upload failed. Please try again.');
        }
    };

    useEffect(() => {
        if (!currentClaim?.id) {
            return;
        }
        setPriceBookLoading(true);
        getClaimPriceBook(currentClaim.id)
            .then((r) => {
                const grouped = r.reduce<Record<string, PriceBookItem[]>>((acc, product) => {
                    acc[product.product_family] = acc[product.product_family] || [];
                    acc[product.product_family].push(product);
                    return acc;
                }, {});

                const remainingKeys = Object.keys(grouped).filter(
                    (key) => !PriceBookSortingGroups.includes(key),
                );

                const sortedGrouped = [...PriceBookSortingGroups, ...remainingKeys].reduce<
                    Record<string, PriceBookItem[]>
                >((acc, key) => {
                    if (grouped[key]) {
                        acc[key] = grouped[key];
                    }
                    return acc;
                }, {});

                setGroupedProducts(sortedGrouped);
            })
            .finally(() => {
                setPriceBookLoading(false);
            });
    }, [currentClaim?.id]);

    useEffect(() => {
        if (!selectedTab && Object.keys(groupedProducts).length > 0) {
            const firstTab = Object.keys(groupedProducts)[0];
            console.log('Setting initial selectedTab to:', firstTab);
            setSelectedTab(firstTab);
        }
    }, [groupedProducts]);

    // Debug form values
    useEffect(() => {
        console.log('Form values changed:', formValues);
        console.log('Group select enabled:', groupSelectEnabled);
        console.log('Temp form values:', tempFormValues);
        console.log('Selected tab:', selectedTab);
    }, [formValues, groupSelectEnabled, tempFormValues, selectedTab]);

    const handleGroupSelectEnabled = (groupSelect: string, value: boolean) => {
        setGroupSelectEnabled((prevState) => ({
            ...prevState,
            [groupSelect]: value,
        }));
    };

    const generateAccordionItems = (groupedProducts: Record<string, PriceBookItem[]>) => {
        return Object.keys(groupedProducts).map((groupName, index) => {
            const selectOptions = groupedProducts[groupName].map((product) => {
                const estimateProductItem = estimateItems[groupName]?.find(
                    (item) => item.product_id === product.product_id,
                );

                return {
                    pricebook_entry_id: product.id,
                    label: product.product_name,
                    name: product.product_name,
                    value: product.id,
                    unit_price: estimateProductItem?.unit_price
                        ? formatNumberOutput(estimateProductItem.unit_price)
                        : undefined,
                    product_id: product.product_id,
                    quantity: estimateProductItem?.quantity ? estimateProductItem.quantity : 1,
                };
            });

            const initialValue =
                (!!Object.keys(estimateItems).length &&
                    estimateItems[groupName] &&
                    selectOptions.filter((item) =>
                        estimateItems[groupName].find(
                            (estimateItem) => estimateItem.product_id === item.product_id,
                        ),
                    )) ||
                [];

            return {
                className: 'claim-ce-form-boxes-item',
                headerClass: 'claim-ce-form-boxes-item-top',
                key: index.toString(),
                label: (
                    <>
                        {groupName}{' '}
                        {!!formValues[groupName]?.length && (
                            <span className="tag">{formValues[groupName]?.length} added</span>
                        )}
                    </>
                ),
                children: (
                    <>
                        <Form.List name={groupName} initialValue={initialValue}>
                            {(fields, { add, remove }) => {
                                return (
                                    <>
                                        <div className="claim-ce-form-boxes-item-head">
                                            <SelectField
                                                className="select"
                                                placeholder="Select a service"
                                                name={`${groupName}-select`}
                                                options={selectOptions}
                                                onOptionChange={() =>
                                                    handleGroupSelectEnabled(
                                                        `${groupName}-select`,
                                                        true,
                                                    )
                                                }
                                            />
                                            <Form.Item>
                                                <Button
                                                    color="primary"
                                                    variant="solid"
                                                    htmlType="button"
                                                    onClick={() => {
                                                        const selectValue =
                                                            form.getFieldValue(groupName)[
                                                                `${groupName}-select`
                                                            ];
                                                        const item = selectOptions.find(
                                                            (item) => item.value === selectValue,
                                                        );
                                                        if (item) {
                                                            add({
                                                                name: item.label,
                                                                pricebook_entry_id:
                                                                    item.pricebook_entry_id,
                                                                product_id: item.product_id,
                                                                quantity: 1,
                                                                unit_price: undefined,
                                                            });
                                                            handleGroupSelectEnabled(
                                                                `${groupName}-select`,
                                                                false,
                                                            );
                                                        }
                                                    }}
                                                    size="large"
                                                    className="margin-right-16"
                                                    wide={true}
                                                    disabled={
                                                        !groupSelectEnabled[`${groupName}-select`]
                                                    }
                                                    icon={<IconPlus className="icon-regular" />}
                                                >
                                                    Add Service
                                                </Button>
                                            </Form.Item>
                                        </div>
                                        <div className="claim-ce-form-boxes-item-list">
                                            <Row className="claim-ce-form-boxes-item-list-head">
                                                <Col span={8}>Service title</Col>
                                                <Col span={4}>Qty</Col>
                                                <Col span={5}>Price</Col>
                                                <Col span={5} className="end">
                                                    Total
                                                </Col>
                                                <Col span={2}></Col>
                                            </Row>
                                            <div className="claim-ce-form-boxes-item-list-body">
                                                {fields &&
                                                    fields.map((field, index) => {
                                                        const selectedServiceName =
                                                            form.getFieldValue([
                                                                groupName,
                                                                field.name,
                                                                'name',
                                                            ]);
                                                        const selectedServicePrice =
                                                            form.getFieldValue([
                                                                groupName,
                                                                field.name,
                                                                'unit_price',
                                                            ]);
                                                        const selectedServiceQuantity =
                                                            form.getFieldValue([
                                                                groupName,
                                                                field.name,
                                                                'quantity',
                                                            ]);
                                                        const selectedServiceLength =
                                                            form.getFieldValue([
                                                                groupName,
                                                                field.name,
                                                                'length',
                                                            ]);

                                                        return (
                                                            <Row key={index}>
                                                                <Col span={8}>
                                                                    <Form.Item
                                                                        name={[field.name, 'name']}
                                                                        noStyle
                                                                    >
                                                                        <span>
                                                                            {selectedServiceName ||
                                                                                'No name selected'}
                                                                        </span>
                                                                    </Form.Item>
                                                                </Col>
                                                                <Col span={4}>
                                                                    <InputField
                                                                        type="number"
                                                                        inputProps={{
                                                                            onWheel: (e) =>
                                                                                e.currentTarget.blur(),
                                                                        }}
                                                                        className="input-wrapper"
                                                                        name={[
                                                                            field.name,
                                                                            'quantity',
                                                                        ]}
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: '',
                                                                            },
                                                                        ]}
                                                                    />
                                                                </Col>
                                                                <Col span={5}>
                                                                    <InputField
                                                                        type="number"
                                                                        inputProps={{
                                                                            onWheel: (e) =>
                                                                                e.currentTarget.blur(),
                                                                        }}
                                                                        className="input-wrapper"
                                                                        name={[
                                                                            field.name,
                                                                            'unit_price',
                                                                        ]}
                                                                        placeholder="0.00"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: '',
                                                                            },
                                                                            {
                                                                                validator: (
                                                                                    _: RuleObject,
                                                                                    value:
                                                                                        | string
                                                                                        | undefined,
                                                                                ) => {
                                                                                    if (
                                                                                        value ===
                                                                                            undefined ||
                                                                                        value ===
                                                                                            null ||
                                                                                        value === ''
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            '',
                                                                                        );
                                                                                    }
                                                                                    if (
                                                                                        isNaN(
                                                                                            Number(
                                                                                                value,
                                                                                            ),
                                                                                        )
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            '',
                                                                                        );
                                                                                    }
                                                                                    return Promise.resolve();
                                                                                },
                                                                            },
                                                                        ]}
                                                                    />
                                                                </Col>
                                                                <Col span={5} className="end">
                                                                    <Form.Item>
                                                                        <span className="claim-ce-form-boxes-item-list-body-total">
                                                                            {formatCurrency(
                                                                                (() => {
                                                                                    const baseTotal = selectedServiceQuantity * selectedServicePrice || 0;
                                                                                    // For Storage items, multiply by length
                                                                                    if (groupName === 'Storage' && selectedServiceLength) {
                                                                                        return baseTotal * selectedServiceLength;
                                                                                    }
                                                                                    return baseTotal;
                                                                                })()
                                                                            )}
                                                                        </span>
                                                                    </Form.Item>
                                                                </Col>
                                                                <Col span={2} className="end">
                                                                    <IconMinusFilled
                                                                        className="icon-regular cursor"
                                                                        onClick={() =>
                                                                            remove(field.name)
                                                                        }
                                                                    />
                                                                </Col>
                                                            </Row>
                                                        );
                                                    })}
                                            </div>
                                        </div>
                                    </>
                                );
                            }}
                        </Form.List>
                    </>
                ),
            };
        });
    };

    const accordionItems = generateAccordionItems(groupedProducts);

    return (
        <Modal
            className="claim-ce"
            onClose={() => setShowConfirmModal(true)}
            show={show}
            size={ModalSize.LARGE}
            title="Add Services"
            sideContent={{
                content: (
                    <div>
                        <div
                            className="claim-header"
                            style={{
                                marginTop: '20px',
                                marginLeft: '20px',
                            }}
                        >
                            <h1 className="heading h3">{currentClaim?.claim_name}</h1>
                            <ClaimStatusTag status={currentClaim?.status} />
                        </div>

                        <div className="claim-ce-content">
                            <div className="claim-ce-info" style={{ marginBottom: '0px' }}>
                                <div
                                    className={classNames('claim-ce-info-inner', {
                                        expanded: infoExpanded,
                                    })}
                                    style={{ background: 'none', paddingTop: '0px' }}
                                >
                                    <h5 className="claim-ce-info-title">Create your estimate</h5>
                                    <div className="claim-ce-info-group">
                                        <p className="claim-ce-info-group-description">
                                            Now it’s time to input your line items. Using our
                                            service page, select a service and enter the
                                            corresponding rate and quantity.
                                            <div
                                                className="claim-ce-info-action"
                                                onClick={() => setInfoExpanded(!infoExpanded)}
                                            >
                                                {infoExpanded ? 'Less' : 'Learn More'}
                                            </div>
                                        </p>
                                    </div>

                                    {infoExpanded && (
                                        <>
                                            <div className="claim-ce-info-group">
                                                <h6 className="claim-ce-info-group-title">
                                                    Helpful Tips!
                                                </h6>
                                                <p className="claim-ce-info-group-description">
                                                    For materials, group them under one service and
                                                    input the total cost for all items from your
                                                    original estimate. This will streamline the
                                                    process and ensure accuracy. Pack Out = Move
                                                    Out. Pack Back = Move Back.
                                                </p>
                                            </div>
                                            <div className="claim-ce-info-group">
                                                <p className="claim-ce-info-group-description">
                                                    <b>Storage:</b> Select the type of storage
                                                    required (e.g., vault or lb) and enter the
                                                    estimated storage needed in Qty. Storage is
                                                    estimated for one month.
                                                </p>
                                            </div>
                                        </>
                                    )}
                                </div>
                            </div>

                            <div className="claim-ce-viewer-head" style={{ padding: '0 30px' }}>
                                <h6 className="claim-ce-viewer-head-title">Original estimate</h6>

                                <div className="claim-ce-viewer-head-actions">
                                    <Button
                                        color="primary"
                                        variant="link"
                                        variation={ButtonVariation.LINK}
                                        onClick={handlePreviewFile}
                                        className="claim-ce-viewer-head-actions-preview"
                                    >
                                        Preview
                                    </Button>
                                    <Button
                                        color="primary"
                                        variant="link"
                                        variation={ButtonVariation.LINK}
                                        onClick={handleChangeFile}
                                        className="claim-ce-viewer-head-actions-change"
                                    >
                                        Change
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                ),
            }}
        >
            {!canSubmit && <Loader className="claim-ce-loader" />}
            <CustomForm form={form} onSubmit={onSubmit} className="claim-ce-form">
                {priceBookLoading && <Loader />}

                {!priceBookLoading && groupNames.length > 0 && (
                    <div className="claim-ce-tab-layout">
                        <div className="claim-ce-tabs">
                            {groupNames.map((group: string) => (
                                <div
                                    key={group}
                                    className={`claim-ce-tab${selectedTab === group ? ' selected' : ''}`}
                                    onClick={() => {
                                        console.log('Switching to tab:', group);
                                        setSelectedTab(group);
                                    }}
                                >
                                    {group}
                                </div>
                            ))}
                        </div>
                        <div className="claim-ce-tab-content">
                            {/* Render the table and form for the selected tab only */}
                            {(() => {
                                const groupName = selectedTab;
                                const selectOptions =
                                    groupedProducts[groupName]?.map((product) => {
                                        const estimateProductItem = estimateItems[groupName]?.find(
                                            (item) => item.product_id === product.product_id,
                                        );
                                        return {
                                            pricebook_entry_id: product.id,
                                            label: product.product_name,
                                            name: product.product_name,
                                            value: product.id,
                                            unit_price: estimateProductItem?.unit_price
                                                ? formatNumberOutput(estimateProductItem.unit_price)
                                                : undefined,
                                            product_id: product.product_id,
                                            quantity: estimateProductItem?.quantity
                                                ? estimateProductItem.quantity
                                                : 1,
                                        };
                                    }) || [];
                                const initialValue =
                                    (!!Object.keys(estimateItems).length &&
                                        estimateItems[groupName] &&
                                        selectOptions.filter((item) =>
                                            estimateItems[groupName].find(
                                                (estimateItem) =>
                                                    estimateItem.product_id === item.product_id,
                                            ),
                                        )) ||
                                    [];
                                return (
                                    <Form.List name={groupName} initialValue={initialValue}>
                                        {(fields, { add, remove }) => (
                                            <>
                                                <div className="claim-ce-form-boxes-item-head">
                                                    <Row
                                                        gutter={16}
                                                        align="bottom"
                                                        style={{ width: '100%' }}
                                                    >
                                                        <Col span={groupName === 'Storage' ? 6 : 8}>
                                                            <Select
                                                                className="select"
                                                                placeholder="Select a Service"
                                                                options={selectOptions}
                                                                value={
                                                                    tempFormValues[
                                                                        `${groupName}_select`
                                                                    ]
                                                                }
                                                                onChange={(value) => {
                                                                    console.log(
                                                                        'Select option changed:',
                                                                        value,
                                                                        'for group:',
                                                                        groupName,
                                                                    );
                                                                    setTempFormValues((prev) => {
                                                                        const newValues = {
                                                                            ...prev,
                                                                            [`${groupName}_select`]:
                                                                                value,
                                                                        };
                                                                        console.log(
                                                                            'Updated tempFormValues:',
                                                                            newValues,
                                                                        );
                                                                        return newValues;
                                                                    });
                                                                    handleGroupSelectEnabled(
                                                                        `${groupName}-select`,
                                                                        true,
                                                                    );
                                                                }}
                                                                style={{ width: '100%' }}
                                                            />
                                                        </Col>
                                                        {groupName === 'Storage' && (
                                                            <Col span={4}>
                                                                <Input
                                                                    type="number"
                                                                    placeholder="Length (Months)"
                                                                    className="input-wrapper"
                                                                    value={
                                                                        tempFormValues[
                                                                            `${groupName}_length`
                                                                        ] || ''
                                                                    }
                                                                    onChange={(e) => {
                                                                        setTempFormValues(
                                                                            (prev) => ({
                                                                                ...prev,
                                                                                [`${groupName}_length`]:
                                                                                    e.target.value,
                                                                            }),
                                                                        );
                                                                    }}
                                                                    style={{ width: '100%' }}
                                                                />
                                                            </Col>
                                                        )}
                                                        <Col span={4}>
                                                            <Input
                                                                type="number"
                                                                placeholder="#"
                                                                className="input-wrapper"
                                                                value={
                                                                    tempFormValues[
                                                                        `${groupName}_quantity`
                                                                    ] || ''
                                                                }
                                                                onChange={(e) => {
                                                                    console.log(
                                                                        'Quantity changed:',
                                                                        e.target.value,
                                                                        'for group:',
                                                                        groupName,
                                                                    );
                                                                    setTempFormValues((prev) => {
                                                                        const newValues = {
                                                                            ...prev,
                                                                            [`${groupName}_quantity`]:
                                                                                e.target.value,
                                                                        };
                                                                        console.log(
                                                                            'Updated tempFormValues:',
                                                                            newValues,
                                                                        );
                                                                        return newValues;
                                                                    });
                                                                }}
                                                                style={{ width: '100%' }}
                                                            />
                                                        </Col>
                                                        <Col span={4}>
                                                            <Input
                                                                type="number"
                                                                placeholder="Rate"
                                                                className="input-wrapper"
                                                                value={
                                                                    tempFormValues[
                                                                        `${groupName}_rate`
                                                                    ] || ''
                                                                }
                                                                onChange={(e) => {
                                                                    setTempFormValues((prev) => ({
                                                                        ...prev,
                                                                        [`${groupName}_rate`]:
                                                                            e.target.value,
                                                                    }));
                                                                }}
                                                                style={{ width: '100%' }}
                                                            />
                                                        </Col>
                                                        <Col span={groupName === 'Storage' ? 6 : 4}>
                                                            <Form.Item style={{ margin: 0 }}>
                                                                <Button
                                                                    color="primary"
                                                                    variant="solid"
                                                                    htmlType="button"
                                                                    onClick={() => {
                                                                        console.log(
                                                                            'ADD button clicked for group:',
                                                                            groupName,
                                                                        );
                                                                        console.log(
                                                                            'Current selectedTab:',
                                                                            selectedTab,
                                                                        );
                                                                        console.log(
                                                                            'All tempFormValues:',
                                                                            tempFormValues,
                                                                        );

                                                                        const selectValue =
                                                                            tempFormValues[
                                                                                `${groupName}_select`
                                                                            ];
                                                                        const lengthValue =
                                                                            groupName === 'Storage'
                                                                                ? tempFormValues[
                                                                                      `${groupName}_length`
                                                                                  ]
                                                                                : undefined;
                                                                        const quantityValue =
                                                                            tempFormValues[
                                                                                `${groupName}_quantity`
                                                                            ];
                                                                        const rateValue =
                                                                            tempFormValues[
                                                                                `${groupName}_rate`
                                                                            ];

                                                                        console.log(
                                                                            'Form values for current group:',
                                                                            {
                                                                                selectValue,
                                                                                lengthValue,
                                                                                quantityValue,
                                                                                rateValue,
                                                                                groupName,
                                                                            },
                                                                        );

                                                                        const item =
                                                                            selectOptions.find(
                                                                                (item) =>
                                                                                    item.value ===
                                                                                    selectValue,
                                                                            );

                                                                        console.log(
                                                                            'Found item:',
                                                                            item,
                                                                        );

                                                                        if (
                                                                            item &&
                                                                            quantityValue &&
                                                                            rateValue
                                                                        ) {
                                                                            const newItem = {
                                                                                name: item.label,
                                                                                pricebook_entry_id:
                                                                                    item.pricebook_entry_id,
                                                                                product_id:
                                                                                    item.product_id,
                                                                                quantity:
                                                                                    Number(
                                                                                        quantityValue,
                                                                                    ),
                                                                                unit_price:
                                                                                    Number(
                                                                                        rateValue,
                                                                                    ),
                                                                                length: lengthValue
                                                                                    ? Number(
                                                                                          lengthValue,
                                                                                      )
                                                                                    : undefined,
                                                                            };

                                                                            console.log(
                                                                                'Adding item to group:',
                                                                                groupName,
                                                                                'Item:',
                                                                                newItem,
                                                                            );
                                                                            console.log(
                                                                                'Current form values before add:',
                                                                                form.getFieldsValue(),
                                                                            );
                                                                            add(newItem);
                                                                            console.log(
                                                                                'Current form values after add:',
                                                                                form.getFieldsValue(),
                                                                            );
                                                                            handleGroupSelectEnabled(
                                                                                `${groupName}-select`,
                                                                                false,
                                                                            );
                                                                            // Clear temp form values
                                                                            console.log(
                                                                                'Clearing form values for group:',
                                                                                groupName,
                                                                            );
                                                                            setTempFormValues(
                                                                                (prev) => {
                                                                                    const newValues =
                                                                                        {
                                                                                            ...prev,
                                                                                            [`${groupName}_select`]:
                                                                                                undefined,
                                                                                            [`${groupName}_length`]:
                                                                                                undefined,
                                                                                            [`${groupName}_quantity`]:
                                                                                                undefined,
                                                                                            [`${groupName}_rate`]:
                                                                                                undefined,
                                                                                        };
                                                                                    console.log(
                                                                                        'Cleared tempFormValues:',
                                                                                        newValues,
                                                                                    );
                                                                                    return newValues;
                                                                                },
                                                                            );
                                                                        } else {
                                                                            console.log(
                                                                                'Missing required fields:',
                                                                                {
                                                                                    hasItem: !!item,
                                                                                    hasQuantity:
                                                                                        !!quantityValue,
                                                                                    hasRate:
                                                                                        !!rateValue,
                                                                                },
                                                                            );
                                                                        }
                                                                    }}
                                                                    size="large"
                                                                    className="margin-right-16"
                                                                    wide={true}
                                                                    disabled={(() => {
                                                                        const selectValue =
                                                                            tempFormValues[
                                                                                `${groupName}_select`
                                                                            ];
                                                                        const quantityValue =
                                                                            tempFormValues[
                                                                                `${groupName}_quantity`
                                                                            ];
                                                                        const rateValue =
                                                                            tempFormValues[
                                                                                `${groupName}_rate`
                                                                            ];

                                                                        const isDisabled =
                                                                            !selectValue ||
                                                                            !quantityValue ||
                                                                            !rateValue;

                                                                        console.log(
                                                                            'Button disabled check:',
                                                                            {
                                                                                groupName,
                                                                                selectValue,
                                                                                quantityValue,
                                                                                rateValue,
                                                                                isDisabled,
                                                                                tempFormValues,
                                                                            },
                                                                        );

                                                                        return isDisabled;
                                                                    })()}
                                                                    icon={
                                                                        <IconPlus className="icon-regular" />
                                                                    }
                                                                    style={{ width: '100%' }}
                                                                >
                                                                    ADD
                                                                </Button>
                                                            </Form.Item>
                                                        </Col>
                                                    </Row>
                                                </div>
                                                <div className="claim-ce-form-boxes-item-list">
                                                    <Row className="claim-ce-form-boxes-item-list-head">
                                                        <Col span={groupName === 'Storage' ? 6 : 8}>
                                                            Service Title
                                                        </Col>
                                                        {groupName === 'Storage' && (
                                                            <Col span={4}>Length (Months)</Col>
                                                        )}
                                                        <Col span={4}>#</Col>
                                                        <Col span={4}>Rate</Col>
                                                        <Col span={4} className="end">
                                                            Total
                                                        </Col>
                                                        <Col span={2}></Col>
                                                    </Row>
                                                    <div className="claim-ce-form-boxes-item-list-body">
                                                        {(() => {
                                                            console.log('Fields array:', fields);
                                                            return null;
                                                        })()}
                                                        {fields &&
                                                            fields.map((field, index) => {
                                                                console.log(
                                                                    'Processing field:',
                                                                    field,
                                                                    'index:',
                                                                    index,
                                                                );
                                                                const selectedServiceName =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'name',
                                                                    ]);
                                                                const selectedServicePrice =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'unit_price',
                                                                    ]);
                                                                const selectedServiceQuantity =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'quantity',
                                                                    ]);
                                                                const selectedServiceLength =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'length',
                                                                    ]);
                                                                return (
                                                                    <Row key={index}>
                                                                        <Col
                                                                            span={
                                                                                groupName ===
                                                                                'Storage'
                                                                                    ? 6
                                                                                    : 8
                                                                            }
                                                                        >
                                                                            <Form.Item
                                                                                name={[
                                                                                    field.name,
                                                                                    'name',
                                                                                ]}
                                                                                noStyle
                                                                            >
                                                                                <span>
                                                                                    {selectedServiceName ||
                                                                                        'No name selected'}
                                                                                </span>
                                                                            </Form.Item>
                                                                        </Col>
                                                                        {groupName ===
                                                                            'Storage' && (
                                                                            <Col span={4}>
                                                                                <span>
                                                                                    {selectedServiceLength ||
                                                                                        selectedServiceQuantity}
                                                                                </span>
                                                                            </Col>
                                                                        )}
                                                                        <Col span={4}>
                                                                            <span>
                                                                                {groupName ===
                                                                                'Storage'
                                                                                    ? selectedServiceQuantity
                                                                                    : `${selectedServiceQuantity} hours`}
                                                                            </span>
                                                                        </Col>
                                                                        <Col span={4}>
                                                                            <span>
                                                                                {formatCurrency(
                                                                                    selectedServicePrice ||
                                                                                        0,
                                                                                )}
                                                                            </span>
                                                                        </Col>
                                                                        <Col
                                                                            span={4}
                                                                            className="end"
                                                                        >
                                                                            <span className="claim-ce-form-boxes-item-list-body-total">
                                                                                {formatCurrency(
                                                                                    (() => {
                                                                                        const baseTotal = selectedServiceQuantity * selectedServicePrice || 0;
                                                                                        // For Storage items, multiply by length
                                                                                        if (groupName === 'Storage' && selectedServiceLength) {
                                                                                            return baseTotal * selectedServiceLength;
                                                                                        }
                                                                                        return baseTotal;
                                                                                    })()
                                                                                )}
                                                                            </span>
                                                                        </Col>
                                                                        <Col
                                                                            span={2}
                                                                            className="end"
                                                                        >
                                                                            <IconMinusFilled
                                                                                className="icon-regular cursor"
                                                                                onClick={() =>
                                                                                    remove(
                                                                                        field.name,
                                                                                    )
                                                                                }
                                                                            />
                                                                        </Col>
                                                                    </Row>
                                                                );
                                                            })}
                                                    </div>
                                                </div>
                                                {/* Subtotal row */}
                                                <div className="claim-ce-form-boxes-item-subtotal-row">
                                                    <span className="claim-ce-form-boxes-item-subtotal-label">
                                                        {groupName} Subtotal
                                                    </span>
                                                    <span className="claim-ce-form-boxes-item-subtotal-value">
                                                        {formatCurrency(
                                                            (() => {
                                                                // Get all form values to ensure we have data from all tabs
                                                                const allFormValues = form.getFieldsValue();
                                                                const groupData = allFormValues[groupName];

                                                                return (groupData?.reduce?.(
                                                                    (sum: number, item: any) => {
                                                                        const baseTotal = Number(item.unit_price) * Number(item.quantity);
                                                                        // For Storage items, multiply by length if it exists
                                                                        if (groupName === 'Storage' && item.length) {
                                                                            return sum + (baseTotal * Number(item.length));
                                                                        }
                                                                        return sum + baseTotal;
                                                                    },
                                                                    0,
                                                                )) || 0;
                                                            })()
                                                        )}
                                                    </span>
                                                </div>
                                            </>
                                        )}
                                    </Form.List>
                                );
                            })()}
                        </div>
                    </div>
                )}

                {!priceBookLoading && !accordionItems.length && (
                    <p>
                        We’re updating the list of available services and it’ll be ready soon! If
                        you need any help in the meantime, don’t hesitate to reach out to us.
                    </p>
                )}
            </CustomForm>

            {/* ESTIMATE SUMMARY Button */}
            <div className="estimate-summary-button-container">
                <Button
                    color="primary"
                    variant="solid"
                    onClick={() => setShowConfirmationModal(true)}
                    className="estimate-summary-button"
                    size="large"
                    // wide={true}
                >
                    ESTIMATE SUMMARY
                </Button>
            </div>

            {showConfirmModal && (
                <Dialog
                    show={showConfirmModal}
                    onClose={() => setShowConfirmModal(false)}
                    onSuccess={() => onClose()}
                    description="If you leave this page, all the information you've entered will be lost. Are you sure you want to continue?"
                />
            )}

            {showConfirmationModal && (
                <EstimateConfirmationModal
                    show={showConfirmationModal}
                    onClose={() => setShowConfirmationModal(false)}
                    onNo={handleConfirmationNo}
                    onYes={handleConfirmationYes}
                />
            )}

            {showEstimateSummary && (
                <EstimateSummaryModal
                    show={showEstimateSummary}
                    onClose={() => setShowEstimateSummary(false)}
                    onGoBack={() => setShowEstimateSummary(false)}
                    onSubmitForApproval={() => {
                        setShowEstimateSummary(false);
                        // Get all form data from all tabs - use the same method as in handleEstimateSummary
                        const allFormData = form.getFieldsValue();

                        // Double-check by getting each group individually
                        const completeFormData: Record<string, any> = {};
                        groupNames.forEach((groupName) => {
                            const groupValue = form.getFieldValue(groupName);
                            if (groupValue && Array.isArray(groupValue) && groupValue.length > 0) {
                                completeFormData[groupName] = groupValue;
                            }
                        });

                        // Use the more complete dataset
                        const dataToSubmit =
                            Object.keys(completeFormData).length > 0
                                ? completeFormData
                                : allFormData;

                        console.log('Submitting form data:', JSON.stringify(dataToSubmit, null, 2));
                        console.log('Groups with data:', Object.keys(dataToSubmit));

                        onSubmit(dataToSubmit);
                    }}
                    items={currentEstimateItems}
                />
            )}

            {/* Preview Modal */}
            {showPreviewModal && (
                <Modal
                    show={showPreviewModal}
                    onClose={() => setShowPreviewModal(false)}
                    size={ModalSize.BIG}
                    title="Original Estimate Preview"
                    className="claim-ce-preview-modal"
                >
                    <div className="claim-ce-preview-content">
                        <FileViewer file={currentFile} />
                    </div>
                </Modal>
            )}

            {/* File Upload Modal */}
            {showFileUploadModal && (
                <Modal
                    show={showFileUploadModal}
                    onClose={() => setShowFileUploadModal(false)}
                    size={ModalSize.MEDIUM}
                    title="Change Original Estimate"
                    className="claim-ce-file-upload-modal"
                >
                    <div className="claim-ce-file-upload-content">
                        <CustomForm onSubmit={handleFileUploadSubmit}>
                            <FileUpload
                                name="file"
                                label="Upload new estimate file"
                                rules={[{ required: true, message: 'Please upload a file!' }]}
                                selectedFile={handleFileSelected}
                                onFinish={handleUploadFinish}
                                maxFileSize={10}
                                showProgressBar={true}
                            >
                                <div className="claim-ce-upload" aria-label="File upload area">
                                    <IconUpload
                                        className="claim-ce-upload-icon"
                                        aria-label="Upload icon"
                                    />
                                    <div className="claim-ce-upload-description">
                                        <div className="claim-ce-upload-description-title">
                                            Select a file or drag and drop here
                                        </div>
                                        <div className="claim-ce-upload-description-label">
                                            (JPG, PNG or PDF, file size no more than 10MB)
                                        </div>
                                    </div>
                                    <Button size="large" htmlType="button">
                                        Select File
                                    </Button>
                                </div>
                            </FileUpload>

                            <div className="claim-ce-file-upload-actions">
                                <Button
                                    color="default"
                                    variant="outlined"
                                    onClick={() => setShowFileUploadModal(false)}
                                    className="claim-ce-file-upload-cancel"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    color="primary"
                                    variant="solid"
                                    htmlType="submit"
                                    className="claim-ce-file-upload-submit"
                                    disabled={!newUploadedFile}
                                >
                                    Upload & Replace
                                </Button>
                            </div>
                        </CustomForm>
                    </div>
                </Modal>
            )}
        </Modal>
    );
};

export default CreateEstimateModal;
