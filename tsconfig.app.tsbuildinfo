{"root": ["./src/main.tsx", "./src/svg.d.ts", "./src/vite-env.d.ts", "./src/@types/index.ts", "./src/common/api.ts", "./src/common/dateformats.ts", "./src/common/promisewithresolvers.ts", "./src/hooks/withauth.tsx", "./src/hooks/withauthredirect.tsx", "./src/hooks/withoutauth.tsx", "./src/modules/app/app.tsx", "./src/modules/app/themeconfig.ts", "./src/modules/app/layouts/authlayout.tsx", "./src/modules/app/layouts/mainlayout.tsx", "./src/modules/app/layouts/sidebar.tsx", "./src/modules/app/layouts/content/content.tsx", "./src/modules/app/layouts/header/header.tsx", "./src/modules/app/layouts/navigation/navigation.tsx", "./src/modules/app/layouts/navigation/navigationitem.tsx", "./src/modules/app/shared/accordion.tsx", "./src/modules/app/shared/button.tsx", "./src/modules/app/shared/claimstatustag.tsx", "./src/modules/app/shared/contextmenu.tsx", "./src/modules/app/shared/customselect.tsx", "./src/modules/app/shared/datagrid.tsx", "./src/modules/app/shared/dialog.tsx", "./src/modules/app/shared/fileviewer.tsx", "./src/modules/app/shared/flashmessages.tsx", "./src/modules/app/shared/loader.tsx", "./src/modules/app/shared/modal.tsx", "./src/modules/app/shared/statusdot.tsx", "./src/modules/app/shared/form/checkboxfield.tsx", "./src/modules/app/shared/form/checkboxgroupfield.tsx", "./src/modules/app/shared/form/fileupload.tsx", "./src/modules/app/shared/form/form.tsx", "./src/modules/app/shared/form/inputfield.tsx", "./src/modules/app/shared/form/inputnumberfield.tsx", "./src/modules/app/shared/form/passwordfield.tsx", "./src/modules/app/shared/form/passwordrequirementslist.tsx", "./src/modules/app/shared/form/radiogroupfield.tsx", "./src/modules/app/shared/form/selectfield.tsx", "./src/modules/app/shared/svg/arrow.tsx", "./src/modules/app/shared/svg/chevron.tsx", "./src/modules/app/shared/svg/dots.tsx", "./src/modules/auth/forgotpasswordpage.tsx", "./src/modules/auth/loginpage.tsx", "./src/modules/auth/resetpasswordpage.tsx", "./src/modules/auth/registration/companyinitial.tsx", "./src/modules/auth/registration/companysecondary.tsx", "./src/modules/auth/registration/personalinfo.tsx", "./src/modules/auth/registration/registrationpage.tsx", "./src/modules/claims/claimslist.tsx", "./src/modules/claims/claimswrapper.tsx", "./src/modules/claims/claim/addcompanycammodal.tsx", "./src/modules/claims/claim/claimpage.tsx", "./src/modules/claims/claim/claimstepbox.tsx", "./src/modules/claims/claim/estimate/createestimatemodal.tsx", "./src/modules/claims/claim/estimate/createestimatepage.tsx", "./src/modules/claims/claim/estimate/createestimatepagewrapper.tsx", "./src/modules/claims/claim/estimate/estimateconfirmationmodal.tsx", "./src/modules/claims/claim/estimate/estimatesummarymodal.tsx", "./src/modules/claims/claim/estimate/estimatesummarypage.tsx", "./src/modules/claims/claim/estimate/estimatesummarypagewrapper.tsx", "./src/modules/claims/claim/estimate/uploadpdfmodal.tsx", "./src/modules/claims/claim/estimate/viewestimatemodal.tsx", "./src/modules/claims/claim/estimate/utils.ts", "./src/modules/claims/claim/scheduledate/availabledates.tsx", "./src/modules/claims/claim/scheduledate/freedatesmodal.tsx", "./src/modules/claims/claim/scheduledate/scheduledatemodal.tsx", "./src/modules/intro/intropage.tsx", "./src/modules/profile/profilepage.tsx", "./src/store/index.ts", "./src/store/auth/authstore.ts", "./src/store/auth/types/authdata.ts", "./src/store/auth/types/authstore.ts", "./src/store/claims/claimstore.ts", "./src/store/claims/types/claim.ts", "./src/store/claims/types/claimstore.ts", "./src/store/file/filestore.ts", "./src/store/file/types/filestore.ts", "./src/store/invitation/invitationstore.ts", "./src/store/invitation/types/invitationdata.ts", "./src/store/invitation/types/invitationstore.ts", "./src/utils/convertto24hours.ts", "./src/utils/converttoampm.ts", "./src/utils/getcompanycamurl.ts", "./src/utils/getstatusclass.ts", "./src/utils/helpers.ts", "./src/utils/isoldsafari.ts", "./src/utils/openlinkinnewtab.ts"], "version": "5.6.2"}