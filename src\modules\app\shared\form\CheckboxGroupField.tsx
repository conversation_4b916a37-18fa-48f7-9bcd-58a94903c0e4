import React from 'react';
import { Form, FormItemProps, Checkbox } from 'antd';
import classNames from 'classnames';

interface CheckboxOption {
    label: React.ReactNode;
    value: string;
}

interface Props extends FormItemProps {
    className?: string;
    label?: React.ReactNode;
    name: string;
    options: CheckboxOption[];
    rules?: any[];
}

const CheckboxGroupField: React.FC<Props> = ({ className, label, name, options, rules, ...props }) => {
    return (
        <Form.Item
            className={classNames("form-field", className)}
            name={name}
            label={label}
            rules={rules}
            {...props}
        >
            <Checkbox.Group className="form-field-checkbox-group">
                {options.map((option) => (
                    <Checkbox key={option.value} value={option.value}>
                        {option.label}
                    </Checkbox>
                ))}
            </Checkbox.Group>
        </Form.Item>
    );
};

export default CheckboxGroupField;
