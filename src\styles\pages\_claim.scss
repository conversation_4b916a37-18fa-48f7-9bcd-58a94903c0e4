.claim {
    font-family: $font-family;

    &-header {
        @include flex(flex, row, wrap, flex-start, center);
        gap: 12px;

        .heading {
            margin-bottom: 0;
        }
    }

    &-box {
        @include flex(flex, column, wrap, flex-start, stretch);
        background-color: $color-background-light;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.02), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.03);
        height: 100%;
        padding: 24px;
        position: relative;

        &-icon {
            @include flex(flex, row, nowrap, flex-start, center);
            gap: 8px;
            position: absolute;
            right: 16px;
            top: 16px;
        }

        &-label {
            font-size: 14px;
            margin-bottom: 8px;
        }

        &-title {
            margin-bottom: 8px;
        }

        &-actions {
            margin-top: auto;
        }

        &-after {
            position: absolute;
            right: -24px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    &-modal {

        // add companycam walkthrough
        &-acw {

            &-actions {
                @include flex(flex, row, wrap, flex-start, stretch);
                gap: 8px;

                .separator {
                    background-color: #E1E2E4;
                    width: 1px;
                }

                .dark {
                    color: $color-primary;
                }
            }

            hr {
                margin: 32px 0;
            }

            .submit {
                margin-top: 8px;
            }
        }

        // schedule walkthrough
        &-sw {

            &-box {
                @include flex(flex, column, wrap, flex-start, flex-stretch);
                background-color: #F2F2F2;
                gap: 4px;
                margin-bottom: 32px;
                padding: 16px;

                &-title {
                    margin-bottom: 4px;
                }
            }
        }
    }

    // create estimate
    &-ce {
        .claim-header {
            margin-bottom: 32px;
        }

        &-upload {
            @include flex(flex, column, wrap, flex-start, center);
            gap: 24px;
            padding: 38px 32px;

            &-icon {
                font-size: 48px;
                margin: 0 auto;
            }

            &-description {

                &-title {
                    font-size: 13px;
                    margin-bottom: 12px;
                }

                &-label {
                    font-size: 13px;
                    opacity: .7;
                }
            }

            .button {
                font-size: 14px;
            }
        }

        &-viewer {

            &-head {
                @include flex(flex, row, wrap, space-between, center);
                gap: 24px;
                margin-bottom: 24px;

                &-title {
                    font-size: 18px;
                    margin-bottom: 0;
                }

                &-actions {
                    @include flex(flex, row, nowrap, flex-start, center);
                    gap: 16px;

                    .button {
                        color: $color-primary;
                        font-size: 12px;
                    }
                }
            }
        }

        .modal-content {

            &-body {
            }
        }

        &-form {
            box-shadow: 0 4px 24px 0 rgba(16,38,100,0.08);
            border-radius: 20px;
            padding: 40px 40px 32px 40px;
            background: #fff;
            
            &-boxes {
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 2px 8px 0 rgba(16,38,100,0.04);
                margin-bottom: 32px;
                
                &-item {
                    border-radius: 14px;
                    margin-bottom: 20px;
                    box-shadow: 0 1px 4px 0 rgba(16,38,100,0.04);
                    background: #fafbfc;
                    
                    &-top {
                        border-radius: 14px 14px 0 0;
                        background: #f5f7fa;
                        padding: 20px 28px 12px 28px;
                        .accordion-header-text {
                            font-size: 18px;
                            font-weight: 600;
                        }
                    }
                    &-head {
                        gap: 20px;
                        padding: 0 28px 12px 28px;
                        .select {
                            border-radius: 8px;
                        }
                        .button {
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: 600;
                            padding: 12px 20px !important;
                        }
                    }
                    &-list {
                        border-radius: 0 0 14px 14px;
                        border: none;
                        background: #fff;
                        box-shadow: 0 1px 4px 0 rgba(16,38,100,0.03);
                        
                        &-head {
                            background: #f0f4f8;
                            font-size: 15px;
                            font-weight: 700;
                            border-radius: 0 0 0 0;
                        }
                        &-body {
                            .ant-col {
                                font-size: 15px;
                                padding-block: 18px;
                                transition: background 0.2s;
                                border-radius: 8px;
                            }
                            .ant-row:hover {
                                background: #f5f7fa;
                            }
                            &-total {
                                font-size: 16px;
                                font-weight: 700;
                                color: $color-primary;
                            }
                        }
                    }
                }
            }
            &-bottom {
                border-radius: 0 0 14px 14px;
                border: none;
                background: #f5f7fa;
                font-size: 20px;
                font-weight: 700;
                gap: 20px;
                margin-bottom: 0;
                padding: 20px 32px;
                box-shadow: 0 -1px 4px 0 rgba(16,38,100,0.03);
                .claim-ce-form-bottom-title {
                    font-size: 18px;
                    font-weight: 600;
                }
            }
        }
        &-info {
            margin-bottom: 40px;
            &-inner {
                max-height: 120px;
                border-radius: 12px;
                background: #f5f7fa;
                padding: 18px 24px;
                &.expanded {
                    max-height: 4000px;
                }
            }
            &-group {
                margin-bottom: 18px;
                &-title {
                    font-size: 16px;
                    font-weight: 600;
                }
                &-description {
                    font-size: 15px;
                    line-height: 1.5;
                    margin-bottom: 0;
                }
            }
            &-action {
                font-size: 14px;
                color: $color-primary;
                font-weight: 600;
                cursor: pointer;
                display: inline-block;
                margin-top: 8px;
                line-height: 1.4;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
        &-loader {
            background: rgba(250,250,250, .7);
            border-radius: 20px;
        }
    }

    // view estimate
    &-ve {

        &-list {
            border: 1px solid #E1E2E4;

            &-head {
                background-color: #F2F2F2;
                border-bottom: 1px solid #E1E2E4;

                .ant-col {
                    padding-bottom: 10px;
                    padding-top: 10px;

                    &:first-child {
                        padding-left: 16px !important;
                    }

                    &:last-child {
                        padding-right: 16px !important;
                    }
                }
            }

            &-body {

                &-group {
                    padding-left: 16px;
                    padding-right: 16px;
                    padding-top: 16px;
                    position: relative;

                    &:after {
                        background-color: #E1E2E4;
                        content: '';
                        bottom: 0;
                        position: absolute;
                        height: 1px;
                        left: 0;
                        width: 100%;
                    }

                    &-title {
                        font-size: 14px;
                        margin-bottom: 8px;
                    }

                    &-content {
                        font-size: 14px;

                        &-row {
                            border-bottom: 1px solid #E1E2E4;

                            .ant-col {
                                display: block;
                                overflow: hidden;
                                padding-bottom: 16px;
                                padding-top: 16px;
                                text-overflow: ellipsis;
                                width: 100%;

                                &:last-child {
                                    border-bottom: none;
                                }

                                &.label {
                                    opacity: .5;
                                    font-weight: $font-weight-bold;
                                }

                                &.highlight {
                                    font-weight: $font-weight-bold;
                                }

                                &.right {
                                    text-align: right;
                                }

                                &.center {
                                    text-align: center;
                                }
                            }
                        }
                    }
                }
            }

            &-bottom {
                @include flex(flex, row, nowrap, space-between, flex-start);
                gap: 16px;
                padding: 16px;

                &-item {
                    font-size: 18px;
                    margin-bottom: 0;
                }
            }
        }
    }
}

// Add styles for top section layout in estimate creation
.claim-ce-top-section {
  @media (max-width: 768px) {
    flex-direction: column !important;
    gap: 16px !important;

    & > div:last-child {
      flex: 1 !important;
    }
  }
}

// Add styles for vertical tab layout in estimate creation modal
.claim-ce-tab-layout {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  margin-top: 24px;
}
.claim-ce-tabs {
  display: flex;
  flex-direction: column;
  min-width: 200px;
  background: #fff;
  border-radius: 12px;
  border: 1.5px solid #e1e2e4;
  padding: 0;
  box-shadow: 0 2px 8px 0 rgba(16,38,100,0.04);
}
.claim-ce-tab {
  padding: 20px 28px;
  font-size: 18px;
  font-weight: 600;
  color: #222;
  background: transparent;
  border: none;
  border-radius: 12px 12px 0 0;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  text-align: left;
  border-bottom: 1px solid #e1e2e4;
}
.claim-ce-tab:last-child {
  border-radius: 0 0 12px 12px;
  border-bottom: none;
}
.claim-ce-tab.selected {
  background: #f5a154;
  color: #fff;
  font-weight: 700;
  border-bottom: none;
  box-shadow: 0 2px 8px 0 rgba(245,161,84,0.08);
}
.claim-ce-tab-content {
  flex: 1;
  min-width: 0;
}
// Table and subtotal row
.claim-ce-form-boxes-item-list {
  border: none;
  background: #fff;
  box-shadow: none;
  border-radius: 12px;
  margin-bottom: 0;
  overflow: hidden;
  box-shadow: 0 2px 8px 0 rgba(16,38,100,0.04);
}
.claim-ce-form-boxes-item-list-head {
  background: #eceaea;
  font-size: 16px;
  font-weight: 700;
  border-radius: 12px 12px 0 0;
  color: #444;
  padding: 16px 20px;
}

.claim-ce-form-boxes-item-list-head .ant-col.end {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.claim-ce-form-boxes-item-list-body {
  background: #fff;
  font-size: 16px;
  color: #222;
}
.claim-ce-form-boxes-item-list-body .ant-row {
  border-bottom: 1px solid #eceaea;
  align-items: center;
  min-height: 60px;
  padding: 16px 20px;
  display: flex;
  width: 100%;
}
.claim-ce-form-boxes-item-list-body .ant-row:last-child {
  border-bottom: none;
}

.claim-ce-form-boxes-item-list-body .ant-col.end {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.claim-ce-form-boxes-item-list-head .ant-col {
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.claim-ce-form-boxes-item-list-body .ant-col {
  display: flex;
  align-items: center;
  padding: 0 8px;
  height: 100%;
  min-height: 28px;
}

.claim-ce-form-boxes-item-list-body .ant-col span {
  width: 100%;
  line-height: 1;
  display: flex;
  align-items: center;
}

.claim-ce-form-boxes-item-list-body .ant-col.end span {
  justify-content: flex-end;
}

// Ensure consistent column widths and alignment
.claim-ce-form-boxes-item-list .ant-row {
  display: flex;
  width: 100%;
}

.claim-ce-form-boxes-item-list .ant-col {
  flex-shrink: 0;
}

// Action column (remove button) alignment
.claim-ce-form-boxes-item-list-body .ant-col:last-child {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Remove any margin/padding from Form.Item in table rows
.claim-ce-form-boxes-item-list-body .ant-form-item {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.claim-ce-form-boxes-item-list-body .ant-form-item-control-input {
  min-height: auto !important;
}

// Ensure total column text is properly aligned
.claim-ce-form-boxes-item-list-body-total {
  font-weight: 600;
  color: #222;
  font-size: 16px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

// Subtotal row
.claim-ce-form-boxes-item-subtotal-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5a15422;
  padding: 16px 20px;
  border-radius: 0 0 12px 12px;
  font-size: 18px;
  font-weight: 700;
  color: #222;
  margin-top: 0;
  margin-bottom: 24px;
}
.claim-ce-form-boxes-item-subtotal-label {
  font-weight: 700;
  color: #222;
}
.claim-ce-form-boxes-item-subtotal-value {
  font-weight: 700;
  color: #222;
}
// Outlined remove and add buttons
.claim-ce-form-boxes-item-list .icon-regular.cursor {
  border: 2px solid #f5a154;
  border-radius: 8px;
  background: #fff;
  color: #f5a154;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s;
}
.claim-ce-form-boxes-item-list .icon-regular.cursor:hover {
  background: #f5a154;
  color: #fff;
}
.claim-ce-form-boxes-item-head .button {
  border: 2px solid #f5a154 !important;
  background: #fff !important;
  color: #f5a154 !important;
  border-radius: 10px !important;
  font-weight: 700 !important;
  font-size: 16px !important;
  min-width: 120px;
  min-height: 48px;
  transition: background 0.2s, color 0.2s;
  padding: 12px 20px !important;
  width: 100% !important;
}
.claim-ce-form-boxes-item-head .button:hover {
  background: #f5a154 !important;
  color: #fff !important;
}
// Add input and select styling for form row
.claim-ce-form-boxes-item-head .select .ant-select-selector,
.claim-ce-form-boxes-item-head input,
.claim-ce-form-boxes-item-head .ant-select-selector {
  border: 1.5px solid #e1e2e4 !important;
  border-radius: 10px !important;
  font-size: 16px !important;
  min-height: 48px !important;
  background: #fff !important;
  padding: 8px 12px !important;
  width: 100% !important;
}

.claim-ce-form-boxes-item-head .ant-select {
  width: 100% !important;
}

.claim-ce-form-boxes-item-head {
  padding: 0 28px 20px 28px;
}

.claim-ce-form-boxes-item-head .ant-row {
  width: 100%;
}

.claim-ce-form-boxes-item-head .ant-col {
  display: flex;
  flex-direction: column;
}

.claim-ce-form-boxes-item-head .ant-form-item {
  margin-bottom: 0 !important;
}

.claim-ce-form-boxes-item-head .ant-row {
  align-items: flex-end;
}

// Ensure proper spacing and alignment for form inputs
.claim-ce-form-boxes-item-head .ant-col {
  min-height: 48px;
  justify-content: flex-end;
}

.claim-ce-form-boxes-item-head .ant-input,
.claim-ce-form-boxes-item-head .ant-select {
  height: 48px !important;
}
// Estimate summary button
.estimate-summary-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 40px;
  padding: 0 40px 32px 40px;
}
.estimate-summary-button {
  background: #444 !important;
  color: #fff !important;
  border-radius: 12px !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  min-width: 280px;
  min-height: 56px;
  box-shadow: 0 4px 12px 0 rgba(16,38,100,0.12);
  border: none !important;
  transition: all 0.2s ease;
}
.estimate-summary-button:hover {
  background: #333 !important;
  box-shadow: 0 6px 16px 0 rgba(16,38,100,0.16);
}

// Estimate summary modal styles
.estimate-summary-content {
  padding: 0;
  max-height: 60vh;
  overflow-y: auto;
  background: #fff;
}

.estimate-summary-section {
  margin-bottom: 0;
}

.estimate-summary-section-header {
  background: #f5a154;
  color: #fff;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 0;
  margin: 0;
}

.estimate-summary-table {
  border: none;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
}

.estimate-summary-table-head {
  background: #f8f8f8;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  padding: 12px 20px;
  margin: 0;
  border-bottom: 1px solid #e1e2e4;
}

.estimate-summary-table-head .ant-col {
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.estimate-summary-table-head .ant-col.end {
  justify-content: flex-end;
}

.estimate-summary-table-body {
  background: #fff;
}

.estimate-summary-table-row {
  padding: 12px 20px;
  border-bottom: 1px solid #e1e2e4;
  font-size: 14px;
  color: #333;
  align-items: center;
  min-height: 48px;
  margin: 0;
  display: flex;
  width: 100%;
}

.estimate-summary-table-row .ant-col {
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-size: 14px;
  color: #333;
  height: 100%;
  min-height: 24px;
  line-height: 1;
}

.estimate-summary-table-row .ant-col.end {
  justify-content: flex-end;
}

.estimate-summary-table-row:last-child {
  border-bottom: 1px solid #e1e2e4;
}

.estimate-summary-subtotal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5a15422;
  padding: 12px 20px;
  border-radius: 0;
  font-size: 16px;
  font-weight: 700;
  color: #333;
  margin: 0;
  border-bottom: 1px solid #e1e2e4;
}

.estimate-summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  padding: 16px 20px;
  border-radius: 0;
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin: 0;
  box-shadow: none;
}

.estimate-summary-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 0;
  padding: 24px;
  border-top: 1px solid #e1e2e4;
  background: #fff;
}

.estimate-summary-back-button {
  background: #fff !important;
  color: #666 !important;
  border: 1px solid #ccc !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  min-width: 100px;
  min-height: 40px;
  padding: 8px 16px !important;
}

.estimate-summary-submit-button {
  background: #444 !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  min-width: 180px;
  min-height: 40px;
  border: none !important;
  padding: 8px 16px !important;
}

// Estimate confirmation modal styles
.estimate-confirmation-content {
  padding: 32px 24px;
  text-align: center;
}

.estimate-confirmation-description {
  font-size: 18px;
  color: #222;
  margin-bottom: 40px;
  line-height: 1.6;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

// Preview and Change buttons styling
.claim-ce-viewer-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #e1e2e4;
  min-height: 60px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    min-height: auto;
  }
}

.claim-ce-viewer-head-title {
  font-size: 18px;
  font-weight: 700;
  color: #222;
  margin: 0;
  flex-shrink: 0;
  line-height: 1.3;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

.claim-ce-viewer-head-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-shrink: 0;

  @media (max-width: 768px) {
    gap: 8px;
    width: 100%;
    justify-content: flex-start;
  }
}

.claim-ce-viewer-head-actions-preview,
.claim-ce-viewer-head-actions-change {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #f5a154 !important;
  text-decoration: none !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease;
}

.claim-ce-viewer-head-actions-preview:hover,
.claim-ce-viewer-head-actions-change:hover {
  background: #f5a15410 !important;
  color: #e8944a !important;
}

// Preview modal styling
.claim-ce-preview-modal .modal-content {
  max-width: 900px;
}

.claim-ce-preview-content {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

// File upload modal styling
.claim-ce-file-upload-content {
  padding: 24px;
}

.claim-ce-file-upload-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e1e2e4;
}

.claim-ce-file-upload-cancel {
  background: #fff !important;
  color: #666 !important;
  border: 1px solid #ccc !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  min-width: 100px;
  min-height: 40px;
  padding: 8px 16px !important;
}

.claim-ce-file-upload-submit {
  background: #f5a154 !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  min-width: 150px;
  min-height: 40px;
  border: none !important;
  padding: 8px 16px !important;
}

.claim-ce-file-upload-submit:disabled {
  background: #ccc !important;
  color: #999 !important;
  cursor: not-allowed !important;
}

// Upload area styling for the file upload modal
.claim-ce-file-upload-modal .claim-ce-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #e1e2e4;
  border-radius: 12px;
  background: #fafafa;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.claim-ce-file-upload-modal .claim-ce-upload:hover {
  border-color: #f5a154;
  background: #f5a15408;
}

.claim-ce-file-upload-modal .claim-ce-upload-icon {
  width: 48px;
  height: 48px;
  color: #f5a154;
  margin-bottom: 16px;
}

.claim-ce-file-upload-modal .claim-ce-upload-description {
  margin-bottom: 24px;
}

.claim-ce-file-upload-modal .claim-ce-upload-description-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  margin-bottom: 8px;
}

.claim-ce-file-upload-modal .claim-ce-upload-description-label {
  font-size: 14px;
  color: #666;
}

.estimate-confirmation-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.estimate-confirmation-no-button {
  background: #fff !important;
  color: #444 !important;
  border: 2px solid #444 !important;
  border-radius: 10px !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  min-width: 120px;
  min-height: 48px;
  transition: all 0.2s ease;
}

.estimate-confirmation-no-button:hover {
  background: #f5f5f5 !important;
}

.estimate-confirmation-yes-button {
  background: #444 !important;
  color: #fff !important;
  border-radius: 10px !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  min-width: 120px;
  min-height: 48px;
  border: none !important;
  transition: all 0.2s ease;
}

.estimate-confirmation-yes-button:hover {
  background: #333 !important;
}

