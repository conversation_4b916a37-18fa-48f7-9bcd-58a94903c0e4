import React from 'react';
import classNames from 'classnames';

interface Props {
    authSideContent?: React.ReactNode;
    children: React.ReactNode;
    className?: string;
    subtitle?: string;
    title?: string;
    variation?: 'center' | 'side';
}

const AuthLayout: React.FC<Props> = ({
    authSideContent,
    children,
    className,
    subtitle,
    title,
    variation = 'center'
}) => {
    return (
        <div
            className={classNames("auth", {
                "center": variation === "center"
            }, className)}
        >
            {/*<div className="auth-overlay"></div>*/}
            
            <div
                className={classNames("auth-content", {
                    "center": variation === "center"
                })}
            >
                {
                    (title || subtitle) &&
                    <div className="auth-top">
                        {title && <h1 className="heading h2">{title}</h1>}
                        
                        {subtitle && <p className="auth-top-text">{subtitle}</p>}
                    </div>
                }
                
                {children}
            </div>
            {
                authSideContent && variation === 'side' &&
                <div className="auth-side">
                    {authSideContent}
                </div>
            }
        </div>
    );
};

export default AuthLayout;
