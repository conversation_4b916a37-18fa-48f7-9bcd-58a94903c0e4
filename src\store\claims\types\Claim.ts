import { PaginationFields } from '../../../@types';

export enum ClaimStatus {
    NEW = 'New projects',
    CLOSED = 'Closed',
    CLOSED_LOST = 'Closed Lost',
    PENDING = 'Pending',
    SCHEDULING = 'Scheduling',
    STORAGE = 'Storage',
}

export interface Claim {
    id: string;
    claim_name: string;
    classification_of_loss: string | null;
    coi_required: boolean;
    company_cam: string | null;
    images_uploaded: boolean;
    insured_email: string | null;
    insured_name: string | null;
    insured_phone: string | null;
    job_number: string;
    loss_city: string | null;
    loss_state: string | null;
    loss_street: string | null;
    loss_zip: string | null;
    project_description: string | null;
    sps_coordinator_email: string | null;
    sps_coordinator_name: string | null;
    sps_coordinator_phone: string | null;
    sq_ft_of_home: number | null;
    status: ClaimStatus;
    storage_required: boolean;
    submitted_date: string | null;
    type_of_move: string[];
    walkthrough_scheduled_time: string | null;
    walkthrough_scheduled_date: string | null;
    walkthrough_type: WalkthroughType | null;
}

export interface ScheduledTimeItem {
    date: string,
    time: string
}

export interface ClaimUpdateFields {
    images_uploaded: boolean;
    walkthrough_scheduled_date: string;
    walkthrough_scheduled_time: string;
}

export interface QueryFields extends PaginationFields {
    status?: ClaimStatus;
}

export enum WalkthroughType {
    AVAILABLE_DATES = 'Available dates',
    SPS_HANDLES_WALKTHROUGH = 'SPS handles walkthrough',
    VENDOR_SCHEDULES = 'Vendor schedules',
    WALKTHROUGH_NOT_NEEDED = 'Walkthrough not needed'
}

export interface Estimate {
    items?: EstimateItem[];
    original_estimate?: string;
    status?: EstimateStatus
}

export interface EstimateItem {
    product_id: string;
    quantity: number;
    unit_price: number;
    product_name: string;
    product_description: string;
    product_family: string;
}

export interface PriceBookItem {
    id: string;
    is_active: boolean;
    unit_price: number;
    product_code: string;
    product_id: string;
    product_name: string;
    product_description: string;
    product_family: string;
}

export interface EstimateCreateProduct {
    pricebook_entry_id: string,
    product_id: string,
    unit_price: number,
    quantity: number
}

export interface EstimateCreate {
    line_item_description?: string;
    original_estimate?: string,
    products?: EstimateCreateProduct[]
}

export enum EstimateStatus {
    ACCEPTED = 'Accepted',
    DENIED = 'Denied',
    DRAFT = 'Draft',
    PRESENTED = 'Presented',
    READY_TO_SEND = 'Ready To Send',
}



