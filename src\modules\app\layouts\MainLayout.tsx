import React from 'react';
import Sidebar from './Sidebar';
import Navigation from './navigation/Navigation';

interface Props {
    children?: React.ReactNode;
}

const MainLayout: React.FC<Props> = ({
    children,
}) => {
    return (
        <div className="main">
            <Sidebar>
                <Navigation />
            </Sidebar>
            <div className="main-content">
                {children}
            </div>
        </div>
    );
};

export default MainLayout;
