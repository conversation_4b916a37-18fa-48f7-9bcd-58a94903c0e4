import React, { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import { format, isToday } from "date-fns";
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Col, Form, Row, TimePicker } from 'antd';
import Button from '../../../app/shared/Button.tsx';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import { DATE_FORMAT_MONTHNAME } from '../../../../common/dateFormats.ts';
import InputField from '../../../app/shared/form/InputField.tsx';
import { convertTo24HourFormat } from '../../../../utils/convertTo24Hours.ts';

interface Props {
    disableForm: boolean;
    onSubmitDate: (date: string, time: string) => void;
}

const today = new Date();
const firstAvailableFormattedDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

const FreeDatesModal: React.FC<Props> = ({disableForm, onSubmitDate}) => {
    const [selectedDate, setSelectedDate] = useState<Date | null>(firstAvailableFormattedDate);
    const [form] = Form.useForm();
    const {currentClaim} = useClaimsContext();
    const [selectedTime, setSelectedTime] = useState<string>();
    
    const handleChange = (date: Date | null) => {
        if (!date) {
            return;
        }
        setSelectedDate(date);
    };
    
    const isDateSelectable = (date: Date) => {
        // console.log(date, new Date());
        return date >= new Date() || isToday(date);
    };
    
    const onSubmit = () => {
        if (!currentClaim?.id || !selectedDate || !selectedTime) {
            return;
        }
        
        const date = new Date(selectedDate);

        const formattedDate = format(date, 'yyyy-M-d');
        
        onSubmitDate(formattedDate, convertTo24HourFormat(selectedTime));
    }
    
    useEffect(() => {
        if (!currentClaim || !selectedDate) {
            return;
        }
        
        form.setFieldValue('date', format(selectedDate, DATE_FORMAT_MONTHNAME))
    }, [selectedDate]);
    
    return (
        <CustomForm
            form={form}
            onSubmit={onSubmit}
        >
            <div className="claim-modal-sw-box">
                <h6 className="claim-modal-sw-box-title">Contact homeowner:</h6>
                {currentClaim?.insured_name && <div className="claim-modal-sw-box-row">{currentClaim.insured_name}</div> }
                {currentClaim?.insured_email && <div className="claim-modal-sw-box-row">{currentClaim.insured_email}</div> }
                {currentClaim?.insured_phone && <div className="claim-modal-sw-box-row">{currentClaim.insured_phone}</div> }
            </div>
            
            <div className="font-weight-bold margin-bottom-16">Set a date:</div>
            
            <DatePicker
                calendarClassName="margin-bottom-24"
                selected={selectedDate}
                onChange={handleChange}
                inline
                filterDate={isDateSelectable}
            />
            
            <Row gutter={[16, 16]}>
                <Col span={12}>
                    <InputField name="date" disabled inputProps={{size: 'small'}} />
                </Col>
                <Col span={12}>
                    <TimePicker
                        use12Hours
                        format="hh:mm A"
                        minuteStep={15}
                        onChange={(_, time) => setSelectedTime(time as string)}
                        placeholder="Select Time"
                    />
                </Col>
            </Row>
            
            <Button
                color="primary"
                variant="solid"
                wide={true}
                disabled={disableForm || !selectedDate || !selectedTime}
            >
                Set a Schedule
            </Button>
        </CustomForm>
    );
};

export default FreeDatesModal;
