import Table, { ColumnsType } from 'antd/es/table';
import { TableRowSelection } from 'antd/es/table/interface';
import { AnyObject } from 'antd/es/_util/type';
import React from 'react';
import { TableProps } from 'antd/lib';

interface Props<T> {
    columns: ColumnsType<T>;
    footer?: React.ReactNode;
    list: any;
    loading?: boolean;
    rowSelection?: TableRowSelection<T>;
    title?: React.ReactNode;
    props?: TableProps<T>
}

function DataGrid<T extends AnyObject>({
    columns,
    footer,
    list,
    loading,
    rowSelection,
    title,
    props
}: Props<T>) {
    return (
        <Table<T>
            rootClassName="table"
            rowKey={(row) => row.id}
            columns={columns}
            dataSource={list}
            pagination={false}
            title={() => title && <div className="table-header">{title}</div>}
            loading={loading}
            rowSelection={rowSelection}
            onChange={props?.onChange}
            footer={footer ? () => <div className="table-footer">{footer}</div> : undefined}
            {...props}
        />
    );
}
export default DataGrid;
