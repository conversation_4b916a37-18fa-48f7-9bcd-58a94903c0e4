import {
    InvitationBusinessContacts,
    InvitationCompanyData,
    InvitationCompanyFormData,
    InvitationPersonalData,
    InvitationStep
} from './InvitationData.ts';
import { PaginationFields } from '../../../@types';

export interface InvitationStore {
    personalData: InvitationPersonalData | null;
    invitationCompanyData: InvitationCompanyData | null;
    
    
    checkToken: (token: string) => Promise<InvitationPersonalData>;
    updatePassword: (token: string, password: string) => Promise<InvitationPersonalData>;
    getCompanyData: (token: string) => Promise<InvitationCompanyData>;
    setCompanyDetailsStep: (token: string, data: Partial<InvitationCompanyFormData>, step: InvitationStep) => Promise<void>;
    getBusinessContacts: (token: string, payload?: PaginationFields) => Promise<InvitationBusinessContacts>;
}
