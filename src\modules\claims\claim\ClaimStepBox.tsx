import React from 'react';
import { ReactComponent as Icon<PERSON><PERSON><PERSON> } from '../../../assets/icons/icon-check.svg';
import Arrow from '../../app/shared/svg/Arrow.tsx';

interface Props {
    actions?: React.ReactNode;
    description?: string;
    index: number;
    showIcon?: boolean;
    tag?: React.ReactNode;
    title: string;
}

const ClaimStepBox: React.FC<Props> = ({
    actions,
    description,
    index,
    showIcon = false,
    tag,
    title
}) => {
    
    return (
        <div className="claim-box">
            {
                (!!tag || showIcon) &&
                <div className="claim-box-icon">
                    {
                        !!tag &&
                        <>{tag}</>
                    }
                    { showIcon && <IconCheck className="icon-regular"/> }
                </div>
            }
            <span className="claim-box-label">
                Step {index}
            </span>
            <h5 className="claim-box-title">{title}</h5>
            {description && <p className="claim-box-text">{description}</p> }
            
            {
                actions &&
                <div className="claim-box-actions">
                    {actions}
                </div>
            }
            {
                index === 1 &&
                <span className="claim-box-after">
                    <Arrow className=" icon-regular"/>
                </span>
            }
        </div>
    )
}

export default ClaimStepBox;
